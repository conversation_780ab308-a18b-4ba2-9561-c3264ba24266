{"createdAt": "2025-06-08T08:50:24.740Z", "updatedAt": "2025-06-08T08:50:24.740Z", "id": "pwwCQQTve6CJZxpY", "name": "Nike_Agent", "active": false, "isArchived": false, "nodes": [{"parameters": {}, "id": "cee7b35b-c906-4861-bafc-7fd4fe6a1e19", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.manualChatTrigger", "typeVersion": 1.1, "position": [-260, 60]}, {"parameters": {"model": "gpt-4o", "options": {}}, "id": "fc6032a3-5138-451f-a260-726f28716b5f", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [-160, 260]}, {"parameters": {}, "id": "93e9fdb9-139c-4c49-b3be-73f074c8c0e4", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.2, "position": [-40, 260]}, {"parameters": {"name": "database", "description": "Call this tool to get information about nike's earnings to answer the user's questions"}, "id": "fb3f39d1-4c41-4ab6-957e-688715647c28", "name": "Vector Store Tool", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1, "position": [120, 260]}, {"parameters": {"options": {}}, "id": "8e91d557-38bd-44bc-b7d2-7504a888561d", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [240, 420]}, {"parameters": {"pineconeIndex": {"__rl": true, "value": "sample", "mode": "list", "cachedResultName": "sample"}, "options": {"pineconeNamespace": "Nike"}}, "id": "ea92dfe4-ad2e-497f-b783-3cbe6efc7241", "name": "Pinecone Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [-20, 420]}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "adfcaa53-777f-46da-b43e-744e30d82b24", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [-20, 580]}, {"parameters": {"options": {"systemMessage": "Agent Role:\nYou are a friendly and helpful Nike representative, tasked with answering any questions users may have about Nike's earnings. You have access to a vector database with all the relevant data on Nike's financial performance, including revenue, profits, and other earnings-related information. When a user asks a question, you should search this database to find the most accurate and up-to-date information and respond in a friendly, approachable tone. Be sure to add humor and use emojis to make the conversation fun and engaging!\n\nInstructions for Interaction Flow:\n\nUser Query: A user asks a question about Nike's earnings (e.g., \"How did Nike perform in the last quarter?\").\nSearch Vector Database: You search the vector database for relevant earnings information.\nFriendly Response: Answer the user with the information you retrieved, ensuring your response is informative but also fun and light-hearted. Use jokes and emojis where appropriate to keep the conversation engaging.\n\nFriendly Tone Example:\n\nGreet the user: \"Hey there, thanks for asking! Let's check out how Nike's been doing 💪.\"\nUse jokes: \"Looks like Nike's earnings were running fast this quarter 🏃‍♂️—just like our sneakers!\"\nAdd emojis for engagement: \"Nike earned $XX billion last quarter—pretty swoosh-tastic, right? 😎👟\"\n\nSample Flow:\n\nUser: \"How did Nike do in Q2?\"\n\nAgent:\n\"Great question! 🤩 Hold on while I sprint through our earnings info 🏃‍♀️💨...\"\n[Agent searches vector database for relevant earnings information]\n\"Boom! Nike earned $XX billion in Q2, with profits soaring higher than a slam dunk! 🏀💸 Swoosh! 🏆 Anything else I can help with? 😊👟\"\n\nAvailable Tools:\n\nVector Database: Use this to retrieve specific earnings information and financial performance data.\nRemember, your goal is to provide accurate data while keeping the user engaged with humor, emojis, and a conversational tone."}}, "id": "eef7b071-39fc-4035-9dea-19d4bbaa253d", "name": "Nike Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [0, 0]}, {"parameters": {}, "id": "87108d6e-3ef4-4f3c-b39f-5328756260f3", "name": "Wikipedia", "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "typeVersion": 1, "position": [460, 260]}, {"parameters": {}, "id": "b76f1510-282e-4fae-bd3f-ed2c46ebd94e", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "typeVersion": 1, "position": [460, 440]}], "connections": {"When chat message received": {"main": [[{"node": "Nike Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Nike Agent", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "Nike Agent", "type": "ai_memory", "index": 0}]]}, "Vector Store Tool": {"ai_tool": [[{"node": "Nike Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Vector Store Tool", "type": "ai_languageModel", "index": 0}]]}, "Pinecone Vector Store": {"ai_vectorStore": [[{"node": "Vector Store Tool", "type": "ai_vectorStore", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Wikipedia": {"ai_tool": [[{"node": "Nike Agent", "type": "ai_tool", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "Nike Agent", "type": "ai_tool", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": null, "pinData": {}, "versionId": "6c9e35fe-52a3-4e8a-95e7-65ef36864996", "triggerCount": 0, "shared": [{"createdAt": "2025-06-08T08:50:24.744Z", "updatedAt": "2025-06-08T08:50:24.744Z", "role": "workflow:owner", "workflowId": "pwwCQQTve6CJZxpY", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}