{"createdAt": "2025-05-30T22:47:23.299Z", "updatedAt": "2025-05-30T22:50:04.000Z", "id": "THUtnFphTINocreZ", "name": "Test Webhook - Simple", "active": false, "isArchived": false, "nodes": [{"parameters": {"path": "test-webhook-simple", "httpMethod": "POST", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [840, 120], "id": "webhook-trigger", "name": "Webhook"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1040, 120], "id": "respond-webhook", "name": "Respond to Webhook"}], "connections": {"Webhook": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": null, "pinData": null, "versionId": "05670b61-34dd-4d63-96ec-41072c08ba95", "triggerCount": 1, "shared": [{"createdAt": "2025-05-30T22:47:23.305Z", "updatedAt": "2025-05-30T22:47:23.305Z", "role": "workflow:owner", "workflowId": "THUtnFphTINocreZ", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}