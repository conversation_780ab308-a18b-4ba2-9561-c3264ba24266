{"createdAt": "2025-06-11T09:08:09.049Z", "updatedAt": "2025-06-11T11:59:02.000Z", "id": "OF59mGdLW92ifIcx", "name": "BNI Presentation Generator - AI Wizard", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "bni-presentation-generator", "responseMode": "responseNode", "options": {"allowedOrigins": "https://bni-presentation-generator.netlify.app,http://localhost:5173,http://localhost:5174,http://localhost:3000,http://localhost:4173"}}, "name": "Presentation Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [220, 160], "id": "webhook-trigger", "webhookId": "bni-presentation-generator"}, {"parameters": {"jsCode": "// Validate and prepare wizard data for AI processing\nconst payload = $input.item.json.body || $input.item.json;\n\nconsole.log('=== BNI PRESENTATION GENERATOR - AI WIZARD ===');\nconsole.log('Timestamp:', new Date().toISOString());\nconsole.log('Payload:', JSON.stringify(payload, null, 2));\n\n// Extract wizard data\nconst wizardData = payload.wizardData || {};\nconst ocrData = payload.ocrData || {};\nconst userId = payload.userId;\nconst title = payload.title || 'Untitled Presentation';\n\n// Validate required fields\nif (!wizardData.goal) {\n  throw new Error('Missing required field: goal');\n}\n\nif (!wizardData.framework) {\n  throw new Error('Missing required field: framework');\n}\n\nif (!userId) {\n  throw new Error('Missing required field: userId');\n}\n\n// Structure data for processing\nconst processedData = {\n  // User and presentation info\n  userId: userId,\n  title: title,\n  timestamp: new Date().toISOString(),\n  \n  // Wizard selections\n  goal: wizardData.goal,\n  audienceLevel: wizardData.audienceLevel || 'mixed',\n  framework: wizardData.framework,\n  hooks: wizardData.hooks || [],\n  stories: wizardData.stories || [],\n  usp: wizardData.usp || '',\n  targetAudience: wizardData.targetAudience || {},\n  enhancements: wizardData.enhancements || [],\n  \n  // OCR extracted data\n  profession: ocrData.profession || '',\n  businessName: ocrData.business_name || '',\n  burningDesire: ocrData.burning_desire || '',\n  keyToSuccess: ocrData.key_to_success || '',\n  skills: ocrData.skills || '',\n  accomplishments: ocrData.accomplishments || '',\n  introductionPhrases: ocrData.introduction_phrases || '',\n  idealReferral: ocrData.ideal_referral_characteristics || '',\n  \n  // Metadata\n  workflowVersion: '1.0',\n  environment: 'production'\n};\n\n// Create presentation context for AI\nconst presentationContext = {\n  memberInfo: {\n    profession: processedData.profession,\n    business: processedData.businessName,\n    mission: processedData.burningDesire,\n    uniqueApproach: processedData.keyToSuccess,\n    expertise: processedData.skills\n  },\n  presentationGoal: {\n    type: processedData.goal,\n    audience: processedData.audienceLevel,\n    framework: processedData.framework\n  },\n  contentElements: {\n    selectedHooks: processedData.hooks,\n    selectedStories: processedData.stories,\n    uniqueSellingPoint: processedData.usp,\n    targetAudienceDetails: processedData.targetAudience,\n    polishElements: processedData.enhancements\n  },\n  referralGuidance: {\n    idealClient: processedData.idealReferral,\n    introductionScript: processedData.introductionPhrases\n  }\n};\n\nreturn {\n  json: {\n    processedData: processedData,\n    presentationContext: presentationContext,\n    originalPayload: payload\n  }\n};"}, "name": "Validate & Structure Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, 160], "id": "validate-data"}, {"parameters": {"jsCode": "// Process and structure the AI-generated presentation\nconst generatedContent = $input.item.json;\nconst processedData = $('Validate & Structure Data').item.json.processedData;\n\nconsole.log('Generated Content:', JSON.stringify(generatedContent, null, 2));\n\n// Parse the generated content\nlet presentation;\ntry {\n  // Check if the content is already parsed\n  if (typeof generatedContent === 'object' && generatedContent.sections) {\n    presentation = generatedContent;\n  } else if (typeof generatedContent === 'string') {\n    presentation = JSON.parse(generatedContent);\n  } else if (generatedContent.value) {\n    // Handle output parser format\n    presentation = JSON.parse(generatedContent.value);\n  } else {\n    throw new Error('Unexpected content format');\n  }\n} catch (error) {\n  console.error('Error parsing generated content:', error);\n  throw new Error('Failed to parse AI-generated content');\n}\n\n// Validate presentation structure\nif (!presentation.sections || !Array.isArray(presentation.sections)) {\n  throw new Error('Invalid presentation structure: missing sections array');\n}\n\nif (presentation.sections.length !== 7) {\n  throw new Error(`Invalid section count: expected 7, got ${presentation.sections.length}`);\n}\n\n// Ensure all sections have required fields\npresentation.sections = presentation.sections.map((section, index) => {\n  return {\n    id: section.id || `section-${index + 1}`,\n    title: section.title || `Section ${index + 1}`,\n    duration: section.duration || 45,\n    script: section.script || '',\n    notes: section.notes || ''\n  };\n});\n\n// Calculate total duration\nconst totalDuration = presentation.sections.reduce((sum, section) => sum + section.duration, 0);\n\n// Create the final presentation object\nconst finalPresentation = {\n  id: generateUUID(),\n  user_id: processedData.userId,\n  title: processedData.title || presentation.title || 'BNI Feature Presentation',\n  wizard_data: {\n    goal: processedData.goal,\n    audienceLevel: processedData.audienceLevel,\n    framework: processedData.framework,\n    hooks: processedData.hooks,\n    stories: processedData.stories,\n    usp: processedData.usp,\n    targetAudience: processedData.targetAudience,\n    enhancements: processedData.enhancements\n  },\n  generated_content: {\n    title: presentation.title,\n    totalDuration: totalDuration,\n    sections: presentation.sections,\n    quickStats: presentation.quickStats || {\n      duration: formatDuration(totalDuration),\n      sections: presentation.sections.length,\n      stories: 2,\n      estimatedValue: 'High impact referral generator'\n    },\n    generatedAt: new Date().toISOString(),\n    version: '1.0'\n  },\n  status: 'completed',\n  created_at: new Date().toISOString(),\n  updated_at: new Date().toISOString()\n};\n\n// Helper functions\nfunction generateUUID() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n}\n\nfunction formatDuration(seconds) {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n}\n\nreturn {\n  json: finalPresentation\n};"}, "name": "Structure Final Presentation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1036, 160], "id": "structure-presentation"}, {"parameters": {"operation": "update", "tableId": "="}, "name": "Save to Supabase", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1260, 160], "id": "save-to-db", "credentials": {"supabaseApi": {"id": "MaYgyPFsCO1qMHKL", "name": "Supabase account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"presentationId\": \"{{ $json.id }}\",\n  \"title\": \"{{ $json.title }}\",\n  \"duration\": \"{{ $json.generated_content.quickStats.duration }}\",\n  \"sections\": {{ $json.generated_content.sections.length }},\n  \"status\": \"{{ $json.status }}\",\n  \"message\": \"Presentation generated successfully\",\n  \"generatedAt\": \"{{ $json.generated_content.generatedAt }}\"\n}", "options": {"responseCode": 200, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Presentation-ID", "value": "={{ $json.id }}"}]}}}, "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1476, 160], "id": "success-response"}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"error\": \"{{ $json.error.message || 'An error occurred during presentation generation' }}\",\n  \"code\": \"{{ $json.error.code || 'GENERATION_ERROR' }}\",\n  \"timestamp\": \"{{ new Date().toISOString() }}\"\n}", "options": {"responseCode": 500, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}]}}}, "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [440, 760], "id": "error-response"}, {"parameters": {"jsCode": "// Catch and format any errors\nconst error = $input.item.error || {};\nconst errorMessage = error.message || 'Unknown error occurred';\nconst errorCode = error.name || 'UNKNOWN_ERROR';\n\nconsole.error('Workflow Error:', {\n  message: errorMessage,\n  code: errorCode,\n  stack: error.stack,\n  timestamp: new Date().toISOString()\n});\n\n// Determine error type and user-friendly message\nlet userMessage = 'An error occurred while generating your presentation';\nlet statusCode = 500;\n\nif (errorMessage.includes('Missing required field')) {\n  userMessage = errorMessage;\n  statusCode = 400;\n} else if (errorMessage.includes('timeout')) {\n  userMessage = 'The presentation generation timed out. Please try again.';\n  statusCode = 504;\n} else if (errorMessage.includes('AI') || errorMessage.includes('OpenAI')) {\n  userMessage = 'The AI service is temporarily unavailable. Please try again in a moment.';\n  statusCode = 503;\n}\n\nreturn {\n  json: {\n    error: {\n      message: userMessage,\n      code: errorCode,\n      details: errorMessage,\n      statusCode: statusCode\n    }\n  }\n};"}, "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [220, 760], "id": "error-handler"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [700, 440], "id": "96bbd844-1664-435e-bce4-0fc3ac68fcf5", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "yn3czriQEae3D6kG", "name": "OpenRouter account"}}}, {"parameters": {"messages": {"messageValues": [{}, {"type": "HumanMessagePromptTemplate"}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [660, 160], "id": "f7841b4e-b170-45de-a5d8-73b808a2ae2c", "name": "Basic LLM Chain"}], "connections": {"Presentation Webhook": {"main": [[{"node": "Validate & Structure Data", "type": "main", "index": 0}]]}, "Validate & Structure Data": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Structure Final Presentation": {"main": [[{"node": "Save to Supabase", "type": "main", "index": 0}]]}, "Save to Supabase": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Error Handler": {"main": [[{"node": "Error Response", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Structure Final Presentation", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "e9f133f4-20d5-404c-b99c-1ac4026b6f71", "triggerCount": 0, "shared": [{"createdAt": "2025-06-11T09:08:09.056Z", "updatedAt": "2025-06-11T09:08:09.056Z", "role": "workflow:owner", "workflowId": "OF59mGdLW92ifIcx", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}