{"createdAt": "2025-06-07T21:51:55.748Z", "updatedAt": "2025-06-07T21:51:55.748Z", "id": "Dphs2uvqf46QSq8K", "name": "BNI Presentation Generator - Production Template", "active": false, "isArchived": false, "workflow_summary": {"description": "Comprehensive BNI presentation generator with dual concept creation, dance card processing, and complete error handling", "key_features": ["Webhook trigger for external integration", "Data validation and structuring", "Optional dance card download and processing", "Dual AI concept generation using OpenAI GPT-4", "JSON parsing and validation", "Supabase database integration", "Comprehensive error handling", "Status tracking and updates"], "workflow_structure": ["Webhook trigger receives presentation request", "Data validation ensures required fields", "Conditional dance card processing", "AI prompt preparation for concept 1", "OpenAI generation and parsing for concept 1", "AI prompt preparation for concept 2 (different approach)", "OpenAI generation and parsing for concept 2", "Results formatting and database storage", "Status updates and response handling"], "integrations": ["OpenAI GPT-4 API", "Supabase database", "HTTP requests for dance card download", "Webhook endpoints"], "error_handling": ["Field validation", "JSON parsing validation", "Database error handling", "Status tracking for failed operations"], "note": "This is a production-ready template with comprehensive BNI presentation generation capabilities. The workflow creates two distinct presentation concepts with different approaches and styles."}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": null, "pinData": null, "versionId": "bf85c07d-ef17-4efc-a58c-86fe3dbafe9d", "triggerCount": 0, "shared": [{"createdAt": "2025-06-07T21:51:55.753Z", "updatedAt": "2025-06-07T21:51:55.753Z", "role": "workflow:owner", "workflowId": "Dphs2uvqf46QSq8K", "projectId": "jncwk4CjUI6FYXvX"}], "tags": []}