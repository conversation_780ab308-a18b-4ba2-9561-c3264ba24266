{"createdAt": "2025-06-27T14:36:32.351Z", "updatedAt": "2025-06-27T14:42:27.000Z", "id": "45CN62PFRW7PQKPD", "name": "Instagram Post Downloads", "active": false, "isArchived": false, "nodes": [{"parameters": {"operation": "write", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-160, 0], "id": "058e2016-5c6b-4041-a932-00da651cbba3", "name": "Read/Write Files from Disk"}, {"parameters": {"options": {"allowFileUploads": true}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-580, 20], "id": "656f05dc-d08d-41f4-ac37-8d1dfc605349", "name": "When chat message received", "webhookId": "30bf0e7c-f9e0-40fc-8d84-da6209ae9dfa"}, {"parameters": {"resource": "webScrapper", "dataset_id": {"__rl": true, "mode": "list"}, "requestOptions": {}}, "type": "@brightdata/n8n-nodes-brightdata.brightData", "typeVersion": 1, "position": [-20, -200], "id": "f3128444-cfbc-4591-bdc0-2e05f8fde42d", "name": "BrightData", "credentials": {"brightdataApi": {"id": "vklaLmNkeZl59FGo", "name": "BrightData account"}}}, {"parameters": {"zone": {"__rl": true, "mode": "list", "value": "web_unlocker1"}, "country": {"__rl": true, "mode": "list", "value": "us"}, "requestOptions": {}}, "type": "@brightdata/n8n-nodes-brightdata.brightData", "typeVersion": 1, "position": [280, 0], "id": "949a1e94-37a8-4b0e-ae30-f3bf184d341e", "name": "BrightData1", "credentials": {"brightdataApi": {"id": "vklaLmNkeZl59FGo", "name": "BrightData account"}}}], "connections": {"BrightData": {"main": [[]]}, "BrightData1": {"main": [[]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "4d927deb-92c2-424c-8a3c-57e5993426d4", "triggerCount": 0, "shared": [{"createdAt": "2025-06-27T14:36:32.358Z", "updatedAt": "2025-06-27T14:36:32.358Z", "role": "workflow:owner", "workflowId": "45CN62PFRW7PQKPD", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}