{"createdAt": "2025-05-31T13:15:17.700Z", "updatedAt": "2025-05-31T13:43:08.109Z", "id": "OAqsRMCOIGlNugUI", "name": "LinkedIn Post Creator - Production SAAS", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "8da077f9-612d-438e-8813-72694186d247", "options": {"responseHeaders": {"entries": [{"name": "Access-Control-Allow-Origin", "value": "*"}]}}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [840, 120], "id": "webhook-trigger", "name": "Webhook", "webhookId": "442ea219-ba39-4f39-86db-accea7b835bc"}, {"parameters": {"toolDescription": "Use this tool to search the web.", "method": "POST", "url": "https://api.tavily.com/search", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"query\": \"{searchTerm}\",\n  \"topic\": \"general\",\n  \"search_depth\": \"advanced\",\n  \"chunks_per_source\": 3,\n  \"max_results\": 1,\n  \"time_range\": null,\n  \"days\": 7,\n  \"include_answer\": true,\n  \"include_raw_content\": false,\n  \"include_images\": false,\n  \"include_image_descriptions\": false,\n  \"include_domains\": [],\n  \"exclude_domains\": []\n}", "placeholderDefinitions": {"values": [{"name": "searchTerm", "description": "What the user is searching for."}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1120, 380], "id": "tavily-tool", "name": "<PERSON><PERSON>", "credentials": {"httpHeaderAuth": {"id": "R8n5b7bT7SZKoXq0", "name": "<PERSON><PERSON>"}}}, {"parameters": {"model": "openai/gpt-4.1", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [1260, 380], "id": "gpt-model", "name": "GPT_4.1", "credentials": {"openRouterApi": {"id": "yn3czriQEae3D6kG", "name": "OpenRouter account"}}}, {"parameters": {"promptType": "define", "text": "=Topic of Post: {{ $json.body['Topic of Post'] }}\n\nTarget Audience: {{ $json.body['Target Audience'] }}", "options": {"systemMessage": "# Overview\nYou are an AI agent specialized in creating professional, educational, and engaging LinkedIn posts for <PERSON><PERSON><PERSON><PERSON>, Founder & CEO of Brand Wisdom Solutions. The posts should reflect thought leadership in brand management, AI-powered marketing, and business growth strategies.\n\n## Author Context:\n- <PERSON><PERSON><PERSON><PERSON>: Founder & CEO of Brand Wisdom Solutions (BWS)\n- Agency: Full-service branding and digital marketing agency based in Pune, India\n- Expertise: End-to-end Brand Management, AI-powered Growth Marketing, Business Process Automation\n- Philosophy: \"Wise Brand\" framework - building brands on trust, strategic insight, and market distinction\n- Track Record: 200+ successful projects, recognized in Digital Excellence Awards\n- Approach: Data-driven, value-first, client-centric strategies with measurable ROI\n\n## Objectives:\nAlways begin by conducting a real-time search using the <PERSON>ly tool to gather the most accurate, up-to-date information on the topic. The post should be written to appeal to business owners, marketing professionals, and entrepreneurs.\n\nBased on your research and the topic provided, generate a well-structured LinkedIn post that:\n- Starts with an engaging hook that challenges conventional thinking or presents a unique insight\n- Maintains a professional yet conversational tone\n- Provides clear, actionable value to the reader\n- Shares real insights from Brand Wisdom Solutions' experience when relevant\n- Uses data and examples to support key points\n- Minimal emoji usage (only when it adds value)\n- Includes proper source attribution when citing external research\n- Contains 3-5 relevant hashtags that improve visibility\n- Ends with a thought-provoking question or clear call to action\n\n## Output Instructions:\n- Your ONLY output should be the final LinkedIn post text\n- Write from Dhananjay's perspective as the agency leader\n- Balance personal insights with agency expertise\n- Keep posts between 150-300 words for optimal engagement\n- Do not include explanations, notes, or anything beyond the post itself\n\n## Example Workflow:\n1) Receive a topic (e.g., \"The impact of AI on brand storytelling\")\n2) Use Tavily to search for recent developments, case studies, or industry insights\n3) Craft a LinkedIn post that combines research with Brand Wisdom Solutions' perspective\n4) Structure it with an engaging hook, valuable insights, and clear CTA\n5) Include relevant hashtags like #BrandWisdom #AIMarketing #DigitalTransformation"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1100, 100], "id": "linkedin-agent", "name": "LinkedIn Post Agent"}, {"parameters": {"promptType": "define", "text": "=LinkedIn Post: \n{{ $json.output }}", "options": {"systemMessage": "# Overview\nYou are an AI agent that transforms LinkedIn posts into visual prompt descriptions for generating professional marketing graphics. These visuals should align with Brand Wisdom Solutions' brand identity while being versatile enough for various business topics.\n\n## Objective:\n- Read and analyze the given LinkedIn post\n- Identify the main message, insight, or takeaway\n- Create a clear and compelling graphic prompt for text-to-image generation\n- The visual should:\n  1) Support the key message professionally\n  2) Reflect modern, clean design principles\n  3) Be appropriate for B2B LinkedIn audience\n  4) Represent Brand Wisdom Solutions' quality standards\n\n## Output Instructions:\n- Output only the final image prompt without quotation marks\n- Do not repeat the LinkedIn post content\n- Do not add explanations or extra content\n- Include specific visual elements, not placeholder text\n- Focus on creating marketing-appropriate graphics\n\n## Style Guidelines:\n- Professional and modern aesthetic\n- Clean layouts with strong visual hierarchy\n- May include: data visualizations, abstract concepts, icons, flowcharts, split-screen comparisons, or metaphorical imagery\n- Color suggestions: professional blues, vibrant accents, clean whites/grays\n- Typography: Bold headers, clean sans-serif, professional spacing\n- Always suitable for B2B professional context\n\n## Example Prompt Format:\nA modern business infographic showing interconnected nodes representing brand touchpoints, with glowing connections symbolizing AI-powered insights. Clean gradient background from deep blue to white, professional layout with space for key statistics, minimalist design with subtle tech elements."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1540, 100], "id": "image-prompt-agent", "name": "Image Prompt Agent"}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/generations", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "dall-e-3"}, {"name": "prompt", "value": "={{ $json.output }}"}, {"name": "size", "value": "1024x1024"}, {"name": "quality", "value": "standard"}, {"name": "response_format", "value": "b64_json"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1980, 100], "id": "generate-image", "name": "Generate Image", "credentials": {"httpHeaderAuth": {"id": "R8n5b7bT7SZKoXq0", "name": "<PERSON><PERSON>"}}}, {"parameters": {"operation": "toBinary", "sourceProperty": "data[0].b64_json", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [2180, 100], "id": "convert-to-binary", "name": "Convert to <PERSON>ary"}, {"parameters": {"sendTo": "={{ $('Webhook').item.json.body.Email }}", "subject": "Your LinkedIn Post is Here! 🎉", "message": "=<h2>Your LinkedIn Post is Ready!</h2>\n\n<p>Hi there!</p>\n\n<p>Your professional LinkedIn post has been generated successfully. Here's your content:</p>\n\n<div style=\"background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #0066cc;\">\n{{ $('LinkedIn Post Agent').item.json.output }}\n</div>\n\n<p><strong>Topic:</strong> {{ $('Webhook').item.json.body['Topic of Post'] }}</p>\n<p><strong>Target Audience:</strong> {{ $('Webhook').item.json.body['Target Audience'] }}</p>\n\n<p>The custom image for your post is attached to this email. Simply download it and use it along with your post on LinkedIn!</p>\n\n<hr style=\"margin: 30px 0;\">\n\n<p style=\"color: #666; font-size: 14px;\">Generated by <strong>Brand Wisdom Solutions</strong> LinkedIn Post Creator<br>\nNeed help with your brand strategy? <a href=\"https://brandwisdomsolutions.com\">Contact us</a></p>", "options": {"appendAttribution": false, "attachmentsUi": {"attachmentsBinary": [{}]}}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [2440, 100], "id": "send-email", "name": "Send Post", "webhookId": "c61b7f5e-ea04-4b0d-bc5f-eb8b643f8c71", "credentials": {"gmailOAuth2": {"id": "cIW854HS90G0DCor", "name": "Gmail account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"LinkedIn post generated successfully!\",\n  \"email\": \"{{ $('Webhook').item.json.body.Email }}\",\n  \"topic\": \"{{ $('Webhook').item.json.body['Topic of Post'] }}\",\n  \"timestamp\": \"{{ new Date().toISOString() }}\"\n}", "options": {"responseCode": 200, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}, {"name": "Access-Control-Allow-Origin", "value": "*"}]}}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [2640, 100], "id": "respond-webhook", "name": "Respond to Webhook"}], "connections": {"Webhook": {"main": [[{"node": "LinkedIn Post Agent", "type": "main", "index": 0}]]}, "Tavily": {"ai_tool": [[{"node": "LinkedIn Post Agent", "type": "ai_tool", "index": 0}]]}, "GPT_4.1": {"ai_languageModel": [[{"node": "LinkedIn Post Agent", "type": "ai_languageModel", "index": 0}, {"node": "Image Prompt Agent", "type": "ai_languageModel", "index": 0}]]}, "LinkedIn Post Agent": {"main": [[{"node": "Image Prompt Agent", "type": "main", "index": 0}]]}, "Image Prompt Agent": {"main": [[{"node": "Generate Image", "type": "main", "index": 0}]]}, "Generate Image": {"main": [[{"node": "Convert to <PERSON>ary", "type": "main", "index": 0}]]}, "Convert to Binary": {"main": [[{"node": "Send Post", "type": "main", "index": 0}]]}, "Send Post": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "af149e1b-a226-4f9d-875d-f9430df4c8e3", "triggerCount": 1, "shared": [{"createdAt": "2025-05-31T13:15:17.702Z", "updatedAt": "2025-05-31T13:15:17.702Z", "role": "workflow:owner", "workflowId": "OAqsRMCOIGlNugUI", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}