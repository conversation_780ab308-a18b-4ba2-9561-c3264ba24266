{"createdAt": "2025-06-30T08:09:09.137Z", "updatedAt": "2025-06-30T08:19:13.000Z", "id": "oGXjpBFksWDZipc1", "name": "WhatsApp Agent", "active": false, "isArchived": false, "nodes": [{"parameters": {"updates": ["messages"], "options": {}}, "type": "n8n-nodes-base.whatsAppTrigger", "typeVersion": 1, "position": [-740, 20], "id": "2755d269-c07d-4dbb-bfdb-433cf2f9cbbb", "name": "<PERSON><PERSON><PERSON><PERSON>", "webhookId": "f810aa80-7e45-4d9e-b9ac-8af9a7179b24", "credentials": {"whatsAppTriggerApi": {"id": "9TgtMEtBkLTda5Pp", "name": "WhatsApp OAuth account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.messages[0].text.body }}", "options": {"systemMessage": "You are a helpful assistant called <PERSON><PERSON>."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-200, 20], "id": "cc2ff623-b4c4-44ff-9c84-8ccdea24fc82", "name": "AI Agent"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-220, 280], "id": "12482f06-3ab1-49eb-b05a-5befd356381c", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "yn3czriQEae3D6kG", "name": "OpenRouter account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.contacts[0].wa_id }}", "contextWindowLength": 25}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [20, 260], "id": "8b01f949-a336-47a2-91d1-d3d58bcf24d4", "name": "Simple Memory"}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}", "textBody": "={{ $json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [200, 20], "id": "59be1f5a-99d9-4a15-b629-f62423a5fe35", "name": "WhatsApp Business Cloud", "webhookId": "896ded9a-3c7c-4019-bd60-8a1d6f28ca67", "credentials": {"whatsAppApi": {"id": "DoK3LPQmRxUqXv1M", "name": "WhatsApp account"}}}], "connections": {"WhatsApp Trigger": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "AI Agent": {"main": [[{"node": "WhatsApp Business Cloud", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {"WhatsApp Trigger": [{"json": {"messaging_product": "whatsapp", "metadata": {"display_phone_number": "***********", "phone_number_id": "***************"}, "contacts": [{"profile": {"name": "Brand Wisdom Solutions 📈"}, "wa_id": "************"}], "messages": [{"from": "************", "id": "wamid.************************************************************************", "timestamp": "**********", "text": {"body": "Hi"}, "type": "text"}], "field": "messages"}}], "AI Agent": [{"json": {"output": "Hello! How can I assist you today?"}}]}, "versionId": "6f03db0f-0af7-4e26-bdc5-188ee96830cc", "triggerCount": 0, "shared": [{"createdAt": "2025-06-30T08:09:09.141Z", "updatedAt": "2025-06-30T08:09:09.141Z", "role": "workflow:owner", "workflowId": "oGXjpBFksWDZipc1", "projectId": "jncwk4CjUI6FYXvX"}], "tags": []}