{"createdAt": "2025-05-30T22:52:15.278Z", "updatedAt": "2025-05-31T14:17:38.000Z", "id": "31t9nWTe22naF3Nh", "name": "LinkedIn SAAS - Fixed Webhook POST", "active": false, "isArchived": false, "workflow_summary": {"description": "LinkedIn content generation SAAS with fixed webhook POST handling", "key_features": ["POST webhook endpoint for external integration", "Tavily web search for real-time information", "AI-powered LinkedIn post generation", "AI-generated image prompts", "OpenAI image generation", "Gmail delivery system", "Webhook response handling"], "main_components": ["Webhook trigger with POST method", "LinkedIn Post Agent with web search", "Image Prompt Agent", "OpenAI image generation", "Gmail delivery system", "Webhook response"], "integrations": ["OpenRouter API (GPT-4.1)", "Tavily search API", "OpenAI image generation API", "Gmail OAuth2", "Webhook endpoints"], "input_format": {"Email": "recipient email", "Topic of Post": "post topic", "Target Audience": "target audience", "timestamp": "ISO timestamp"}, "note": "This is a production SAAS workflow with comprehensive LinkedIn content generation capabilities."}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": null, "versionId": "08dd2db6-9828-49c3-a6df-57c7c5bf0ed1", "triggerCount": 1, "shared": [{"createdAt": "2025-05-30T22:52:15.281Z", "updatedAt": "2025-05-30T22:52:15.281Z", "role": "workflow:owner", "workflowId": "31t9nWTe22naF3Nh", "projectId": "jncwk4CjUI6FYXvX"}], "tags": []}