{"createdAt": "2025-05-31T10:45:34.252Z", "updatedAt": "2025-05-31T10:48:39.703Z", "id": "eZSrd2vfXdDxNfoP", "name": "LinkedIn Post Generator - Final Working", "active": false, "isArchived": false, "nodes": [{"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "c7e0d129-c68b-426e-86d5-301dfdab9648", "leftValue": "={{ $json.headers['x-http-method'] }}", "rightValue": "OPTIONS", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [400, 300], "id": "handle-cors", "name": "Check if OPTIONS"}, {"parameters": {"options": {"responseCode": 200, "responseHeaders": {"entries": [{"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"name": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}}, "respondWith": "noData"}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [600, 180], "id": "cors-response", "name": "CORS Response"}, {"parameters": {"path": "linkedin-post-generator", "options": {"rawBody": false}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [200, 300], "id": "webhook-trigger", "name": "Webhook"}, {"parameters": {"toolDescription": "Use this tool to search the web.", "method": "POST", "url": "https://api.tavily.com/search", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"query\": \"{searchTerm}\",\n  \"topic\": \"general\",\n  \"search_depth\": \"advanced\",\n  \"chunks_per_source\": 3,\n  \"max_results\": 1,\n  \"time_range\": null,\n  \"days\": 7,\n  \"include_answer\": true,\n  \"include_raw_content\": false,\n  \"include_images\": false,\n  \"include_image_descriptions\": false,\n  \"include_domains\": [],\n  \"exclude_domains\": []\n}", "placeholderDefinitions": {"values": [{"name": "searchTerm", "description": "What the user is searching for."}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [800, 500], "id": "tavily-tool", "name": "<PERSON><PERSON>", "credentials": {"httpHeaderAuth": {"id": "R8n5b7bT7SZKoXq0", "name": "<PERSON><PERSON>"}}}, {"parameters": {"model": "openai/gpt-4.1", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [1000, 500], "id": "gpt-model", "name": "GPT-4 Model", "credentials": {"openRouterApi": {"id": "yn3czriQEae3D6kG", "name": "OpenRouter account"}}}]}