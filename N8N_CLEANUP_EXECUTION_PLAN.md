# n8n Platform Cleanup - Detailed Execution Plan

## OBJECTIVE
Remove 26 workflows from n8n platform, keeping only 15 essential workflows (4 Working + 1 Fixable + 10 High-Value)

## BACKUP STATUS
✅ **ALL WORKFLOWS BACKED UP** - Complete local backups verified for all 41 workflows

---

## 15 WORKFLOWS TO KEEP (DO NOT DELETE)

### 4 WORKING WORKFLOWS (Production Ready)
1. `31t9nWTe22naF3Nh` - LinkedIn SAAS - Fixed Webhook POST
2. `3s5AnC3aQ3oXYEaV` - My workflow 3  
3. `J4GmoGWcrMz2argl` - BNI Document Processor v2 - OpenRouter Agent
4. `oGXjpBFksWDZipc1` - WhatsApp Agent

### 1 FIXABLE WORKFLOW (Timeout Optimization)
5. `XJDEslazu5PowwxK` - BNI Dance Card OCR - Comprehensive Fields

### 10 HIGH-VALUE WORKFLOWS (Ready for Completion)
6. `6xzd7MbVuHV0299h` - AI Asset Generator in n8n
7. `cS3fL1KamKDXMwWE` - ## 🚀 Ultimate n8n Agentic RAG Template
8. `45CN62PFRW7PQKPD` - Instagram Post Downloads
9. `M23PjOZS4h2a3X48` - My workflow 4
10. `pwwCQQTve6CJZxpY` - Nike_Agent
11. `xh2mbNMbrd69UQfc` - Supabase_Postgres
12. `D2dSFnvKxbXdLxj6` - BNI Dance Card OCR - Production
13. `9vr6XGinOFYqKAKy` - Presentation 1.0
14. `bstuMX4ETPaZ22uZ` - Basic Working Image Genratin Workflow For SAAS
15. `QKyqFqowmc1Xyk3s` - My preferred Rough floow.

---

## 26 WORKFLOWS TO DELETE (Organized in 6 Batches)

### BATCH 1: BNI Duplicates (5 workflows)
**Status**: ⚠️ PARTIALLY STARTED (1 deleted: A64SXgBk1xQ7CXyC)
1. ~~`A64SXgBk1xQ7CXyC` - BNI Presentation Generator - Production Ready~~ ✅ DELETED
2. `CgkkDjPVNM01fAoT` - BNI Simple Webhook Test
3. `Dphs2uvqf46QSq8K` - BNI Presentation Generator - Production Template  
4. `IxPm3WbckIoL6Z0j` - BNI Document Processor v2 - OpenRouter Agent copy
5. `JKShE98n1SuvXZt7` - BNI Presentation Generator - Clean Architecture

### BATCH 2: LinkedIn Duplicates Set 1 (5 workflows)
**Status**: 🔄 PENDING
1. `OAqsRMCOIGlNugUI` - LinkedIn Post Creator - Production SAAS
2. `R2OxWRZgdEKlJ8OD` - LinkedIn Post Creator - App Integration Fixed
3. `V75UszgK8xkZx3rh` - LinkedIn Post Generator - Working SAAS
4. `dfVLVRofmCqVrgQX` - LinkedIn Post Generator - App Integration
5. `eZSrd2vfXdDxNfoP` - LinkedIn Post Generator - Final Working

### BATCH 3: LinkedIn Duplicates Set 2 (5 workflows)
**Status**: 🔄 PENDING
1. `gbEzcKCaDHSscKfT` - LinkedIn Post Generator - App Ready
2. `h4oMeLIF0xBFzR3X` - BNI Presentation Generator - Production Template Backup
3. `iDPDnaP86iStLXCS` - LinkedIn Post Generator - Final SAAS
4. `oAtzaqulNaNDKWDK` - LinkedIn SAAS - Corrected Integration
5. `scUBjA3u5C0rpR02` - LinkedIn SAAS - Correct Integration

### BATCH 4: LinkedIn + BNI Duplicates (5 workflows)
**Status**: 🔄 PENDING
1. `tJa1bYVc2KqEtmkd` - LinkedIn SAAS - Production Ready v2
2. `syQBLV9AerRPorGv` - BNI Presentation Generator - Fixed Supabase Structure
3. `lAv8NYG3hiFnNPZm` - BNI Document Processor - Dance Card Analysis
4. `cOXLUxmPm8LFRkVR` - BNI Document Processor v3 - Mistral OCR Test
5. `OF59mGdLW92ifIcx` - BNI Presentation Generator - AI Wizard

### BATCH 5: Test Workflows (5 workflows)
**Status**: 🔄 PENDING
1. `THUtnFphTINocreZ` - Test Webhook - Simple
2. `wLRrus2lZJtxBlLA` - Test Webhook - CORS Debug
3. `nWzd60SsNCOJM8oi` - BNI Test Webhook - Data Reception Verification
4. `fbDGDcT2EDAtVLXZ` - BNI Test - Properly Configured Webhook
5. `ksX2FAoyw15Tq2Kn` - My workflow 2

### BATCH 6: Final Miscellaneous (1 workflow)
**Status**: 🔄 PENDING
1. `sysnuvEgjc6w7Yjv` - My workflow

---

## EXECUTION COMMANDS FOR NEXT SESSION

### Resume from Batch 1 (Complete remaining 4):
```
delete_workflow_n8n-vps-brandwisdom: CgkkDjPVNM01fAoT
delete_workflow_n8n-vps-brandwisdom: Dphs2uvqf46QSq8K  
delete_workflow_n8n-vps-brandwisdom: IxPm3WbckIoL6Z0j
delete_workflow_n8n-vps-brandwisdom: JKShE98n1SuvXZt7
```

### Batch 2 Commands:
```
delete_workflow_n8n-vps-brandwisdom: OAqsRMCOIGlNugUI
delete_workflow_n8n-vps-brandwisdom: R2OxWRZgdEKlJ8OD
delete_workflow_n8n-vps-brandwisdom: V75UszgK8xkZx3rh
delete_workflow_n8n-vps-brandwisdom: dfVLVRofmCqVrgQX
delete_workflow_n8n-vps-brandwisdom: eZSrd2vfXdDxNfoP
```

### Batch 3 Commands:
```
delete_workflow_n8n-vps-brandwisdom: gbEzcKCaDHSscKfT
delete_workflow_n8n-vps-brandwisdom: h4oMeLIF0xBFzR3X
delete_workflow_n8n-vps-brandwisdom: iDPDnaP86iStLXCS
delete_workflow_n8n-vps-brandwisdom: oAtzaqulNaNDKWDK
delete_workflow_n8n-vps-brandwisdom: scUBjA3u5C0rpR02
```

### Batch 4 Commands:
```
delete_workflow_n8n-vps-brandwisdom: tJa1bYVc2KqEtmkd
delete_workflow_n8n-vps-brandwisdom: syQBLV9AerRPorGv
delete_workflow_n8n-vps-brandwisdom: lAv8NYG3hiFnNPZm
delete_workflow_n8n-vps-brandwisdom: cOXLUxmPm8LFRkVR
delete_workflow_n8n-vps-brandwisdom: OF59mGdLW92ifIcx
```

### Batch 5 Commands:
```
delete_workflow_n8n-vps-brandwisdom: THUtnFphTINocreZ
delete_workflow_n8n-vps-brandwisdom: wLRrus2lZJtxBlLA
delete_workflow_n8n-vps-brandwisdom: nWzd60SsNCOJM8oi
delete_workflow_n8n-vps-brandwisdom: fbDGDcT2EDAtVLXZ
delete_workflow_n8n-vps-brandwisdom: ksX2FAoyw15Tq2Kn
```

### Batch 6 Commands:
```
delete_workflow_n8n-vps-brandwisdom: sysnuvEgjc6w7Yjv
```

---

## VERIFICATION COMMANDS

### Check Current Workflow Count:
```
list_workflows_n8n-vps-brandwisdom
```

### Expected Final Result:
- **Total workflows**: 15 (down from 41)
- **Deleted workflows**: 26
- **All 15 keepers**: Confirmed present and untouched

---

## SAFETY NOTES
- ✅ All 41 workflows have complete local backups
- ✅ Backup verification completed successfully  
- ✅ 1 workflow already deleted (A64SXgBk1xQ7CXyC)
- ⚠️ 25 workflows remaining to delete
- 🎯 Target: Exactly 15 workflows remaining on platform

## CURRENT STATUS
**Progress**: 1/26 workflows deleted (3.8% complete)
**Next Action**: Complete Batch 1 (4 remaining workflows)
**Session Continuation**: Ready for immediate resumption
