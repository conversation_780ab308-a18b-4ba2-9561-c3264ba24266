{"createdAt": "2025-06-23T04:49:48.923Z", "updatedAt": "2025-06-23T07:49:20.000Z", "id": "3s5AnC3aQ3oXYEaV", "name": "My workflow 3", "active": false, "isArchived": false, "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-600, 20], "id": "b2ed1208-003a-454e-9541-6c2321859b04", "name": "When clicking 'Execute workflow'"}, {"parameters": {"fileSelector": "J:\\Brand Wisdom\\Resources\\n8n\\Official Docs", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-280, 0], "id": "45c960d3-04a1-4658-a00b-d28a2e89aa2c", "name": "Read/Write Files from Disk"}], "connections": {"When clicking 'Execute workflow'": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "eb0f81b9-b626-4018-a348-4d6ceada9e3b", "triggerCount": 0, "shared": [{"createdAt": "2025-06-23T04:49:48.928Z", "updatedAt": "2025-06-23T04:49:48.928Z", "role": "workflow:owner", "workflowId": "3s5AnC3aQ3oXYEaV", "projectId": "jncwk4CjUI6FYXvX"}], "tags": []}