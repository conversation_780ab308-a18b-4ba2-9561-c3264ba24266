{"createdAt": "2025-06-11T12:03:14.682Z", "updatedAt": "2025-06-13T12:23:21.000Z", "id": "ksX2FAoyw15Tq2Kn", "name": "My workflow 2", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "bni-presentation-generator", "responseMode": "responseNode", "options": {"allowedOrigins": "https://bni-presentation-generator.netlify.app,http://localhost:5173,http://localhost:5174,http://localhost:3000,http://localhost:4173"}}, "name": "Presentation Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, -100], "id": "18e19fb2-fd4e-4c66-9938-b3458436ce0d", "webhookId": "bni-presentation-generator"}, {"parameters": {"jsCode": "// Validate and prepare wizard data for AI processing\nconst payload = $input.item.json.body || $input.item.json;\n\nconsole.log('=== BNI PRESENTATION GENERATOR ===');\nconsole.log('Timestamp:', new Date().toISOString());\nconsole.log('Payload:', JSON.stringify(payload, null, 2));\n\n// Extract wizard data\nconst wizardData = payload.wizardData || {};\nconst ocrData = payload.ocrData || {};\nconst userId = payload.userId;\nconst title = payload.title || 'Untitled Presentation';\n\n// Validate required fields\nif (!wizardData.goal) {\n  throw new Error('Missing required field: goal');\n}\n\nif (!wizardData.framework) {\n  throw new Error('Missing required field: framework');\n}\n\nif (!userId) {\n  throw new Error('Missing required field: userId');\n}\n\n// Create presentation context for AI\nconst presentationContext = {\n  memberInfo: {\n    name: wizardData.name || ocrData.business_name || '',\n    profession: ocrData.profession || wizardData.profession || '',\n    business: ocrData.business_name || wizardData.company || '',\n    mission: ocrData.burning_desire || wizardData.burningDesire || '',\n    uniqueApproach: ocrData.key_to_success || wizardData.keyToSuccess || '',\n    expertise: ocrData.skills || wizardData.skills || '',\n    accomplishments: ocrData.accomplishments || ''\n  },\n  presentationGoal: {\n    type: wizardData.goal,\n    audience: wizardData.audienceLevel || 'mixed',\n    framework: wizardData.framework\n  },\n  contentElements: {\n    selectedHooks: wizardData.hookType || '',\n    selectedStories: wizardData.stories || [],\n    uniqueSellingPoint: wizardData.usp || '',\n    testimonial: wizardData.testimonial || '',\n    visualAids: wizardData.visualAids || [],\n    polishElements: wizardData.enhancements || []\n  },\n  referralGuidance: {\n    idealClient: ocrData.ideal_referral_characteristics || wizardData.idealReferral || '',\n    targets: wizardData.targets || '',\n    phrases: wizardData.phrases || '',\n    triggers: wizardData.triggers || '',\n    introductionScript: ocrData.introduction_phrases || wizardData.introductionPhrases || ''\n  },\n  specialRequests: {\n    doorPrize: wizardData.doorPrize || '',\n    specialInstructions: wizardData.specialInstructions || ''\n  }\n};\n\nreturn {\n  json: {\n    presentationContext: presentationContext,\n    metadata: {\n      userId: userId,\n      title: title,\n      timestamp: new Date().toISOString()\n    }\n  }\n};"}, "name": "Validate & Structure Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, -100], "id": "cd287e79-dd87-4668-a989-4599dfb857b5"}, {"parameters": {"hasOutputParser": true, "messages": {"messageValues": [{"message": "You are an expert BNI presentation coach specializing in creating compelling 8-10 minute feature presentations. You understand BNI culture, values, and the importance of building trust and generating referrals.\n\nYour presentations should:\n1. Be conversational and authentic\n2. Include specific examples and stories\n3. Build emotional connection\n4. Have clear referral asks\n5. Follow BNI best practices\n6. Be exactly 8 minutes long (480 seconds total)"}, {"type": "HumanMessagePromptTemplate", "message": "Create a compelling BNI 8-10 minute feature presentation with exactly 7 sections based on this information:\n\nMember Information:\n- Name: {{$json.presentationContext.memberInfo.name}}\n- Profession: {{$json.presentationContext.memberInfo.profession}}\n- Business: {{$json.presentationContext.memberInfo.business}}\n- Mission/Burning Desire: {{$json.presentationContext.memberInfo.mission}}\n- Unique Approach: {{$json.presentationContext.memberInfo.uniqueApproach}}\n- Expertise/Skills: {{$json.presentationContext.memberInfo.expertise}}\n- Accomplishments: {{$json.presentationContext.memberInfo.accomplishments}}\n\nPresentation Requirements:\n- Goal: {{$json.presentationContext.presentationGoal.type}}\n- Audience Level: {{$json.presentationContext.presentationGoal.audience}}\n- Framework: {{$json.presentationContext.presentationGoal.framework}}\n\nSelected Content:\n- Hook Type: {{$json.presentationContext.contentElements.selectedHooks}}\n- Stories to Include: {{$json.presentationContext.contentElements.selectedStories}}\n- USP: {{$json.presentationContext.contentElements.uniqueSellingPoint}}\n- Testimonial: {{$json.presentationContext.contentElements.testimonial}}\n- Visual Aids: {{$json.presentationContext.contentElements.visualAids}}\n- Polish Elements: {{$json.presentationContext.contentElements.polishElements}}\n\nReferral Information:\n- Ideal Client: {{$json.presentationContext.referralGuidance.idealClient}}\n- Target Markets: {{$json.presentationContext.referralGuidance.targets}}\n- Key Phrases: {{$json.presentationContext.referralGuidance.phrases}}\n- Referral Triggers: {{$json.presentationContext.referralGuidance.triggers}}\n- Introduction Script: {{$json.presentationContext.referralGuidance.introductionScript}}\n\nSpecial Elements:\n- Door Prize: {{$json.presentationContext.specialRequests.doorPrize}}\n- Special Instructions: {{$json.presentationContext.specialRequests.specialInstructions}}\n\nGenerate a JSON response with this EXACT structure:\n{\n  \"title\": \"[Create an engaging presentation title based on the goal]\",\n  \"sections\": [\n    {\n      \"id\": 1,\n      \"title\": \"The Hook\",\n      \"timing\": \"0:00 - 0:30\",\n      \"duration\": 30,\n      \"description\": \"Grab attention from the first second\",\n      \"script\": \"[Write the exact words to speak, incorporating the selected hook type]\",\n      \"notes\": \"[Speaker notes: staging, tone, gestures, pauses]\"\n    },\n    {\n      \"id\": 2,\n      \"title\": \"Personal Connection\",\n      \"timing\": \"0:30 - 1:30\",\n      \"duration\": 60,\n      \"description\": \"Build trust through vulnerability\",\n      \"script\": \"[Personal story that connects to the business mission]\",\n      \"notes\": \"[Delivery tips for authentic connection]\"\n    },\n    {\n      \"id\": 3,\n      \"title\": \"The Problem\",\n      \"timing\": \"1:30 - 2:30\",\n      \"duration\": 60,\n      \"description\": \"Agitate the pain your audience feels\",\n      \"script\": \"[Describe the problems your ideal clients face]\",\n      \"notes\": \"[How to show empathy and understanding]\"\n    },\n    {\n      \"id\": 4,\n      \"title\": \"The Solution\",\n      \"timing\": \"2:30 - 4:00\",\n      \"duration\": 90,\n      \"description\": \"Present your unique approach\",\n      \"script\": \"[Explain your unique approach and methodology]\",\n      \"notes\": \"[Emphasize what makes you different]\"\n    },\n    {\n      \"id\": 5,\n      \"title\": \"Success Stories\",\n      \"timing\": \"4:00 - 5:30\",\n      \"duration\": 90,\n      \"description\": \"Prove it works with examples\",\n      \"script\": \"[Share 2-3 specific client success stories]\",\n      \"notes\": \"[Use names, numbers, and specific outcomes]\"\n    },\n    {\n      \"id\": 6,\n      \"title\": \"The Referral Ask\",\n      \"timing\": \"5:30 - 7:00\",\n      \"duration\": 90,\n      \"description\": \"Specific requests for ideal referrals\",\n      \"script\": \"[Clearly describe your ideal referrals using the provided information]\",\n      \"notes\": \"[Be specific about who, what situations, and how to introduce]\"\n    },\n    {\n      \"id\": 7,\n      \"title\": \"The Close\",\n      \"timing\": \"7:00 - 8:00\",\n      \"duration\": 60,\n      \"description\": \"Memorable finish with clear next steps\",\n      \"script\": \"[Strong closing with call to action and memorable tagline]\",\n      \"notes\": \"[End with energy and clear next steps]\"\n    }\n  ],\n  \"metadata\": {\n    \"totalDuration\": 480,\n    \"framework\": \"{{$json.presentationContext.presentationGoal.framework}}\",\n    \"goal\": \"{{$json.presentationContext.presentationGoal.type}}\"\n  }\n}\n\nIMPORTANT:\n1. Make the script conversational and natural to speak\n2. Include specific examples relevant to the profession\n3. Ensure total duration equals exactly 480 seconds (8 minutes)\n4. Incorporate all selected content elements naturally\n5. Create a presentation that will generate referrals\n6. Use the member's actual business information, not placeholders"}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [680, -100], "id": "1fe3ca7c-b88f-44b2-9768-b76fd9737e43", "name": "Generate Presentation with AI"}, {"parameters": {"model": "deepseek/deepseek-r1-0528:free", "options": {"maxTokens": 8000, "temperature": 0.7}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [680, 120], "id": "f1407aa4-1fad-47b7-ad43-1984e30061da", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "yn3czriQEae3D6kG", "name": "OpenRouter account"}}}, {"parameters": {"jsCode": "// Process and structure the AI-generated presentation\nconst aiOutput = $input.item.json;\nconst context = $('Validate & Structure Data').item.json;\n\nconsole.log('AI Output:', JSON.stringify(aiOutput, null, 2));\n\n// Parse the generated content\nlet presentation;\ntry {\n  // Try to parse as JSON first\n  if (typeof aiOutput === 'string') {\n    presentation = JSON.parse(aiOutput);\n  } else if (aiOutput.response) {\n    presentation = typeof aiOutput.response === 'string' ? JSON.parse(aiOutput.response) : aiOutput.response;\n  } else if (aiOutput.text) {\n    presentation = JSON.parse(aiOutput.text);\n  } else {\n    presentation = aiOutput;\n  }\n} catch (error) {\n  console.error('Error parsing AI output:', error);\n  // Try to extract <PERSON><PERSON><PERSON> from the text\n  const jsonMatch = JSON.stringify(aiOutput).match(/\\{[\\s\\S]*\\}/)?.[0];\n  if (jsonMatch) {\n    try {\n      presentation = JSON.parse(jsonMatch);\n    } catch (e) {\n      throw new Error('Failed to parse AI-generated content as JSON');\n    }\n  } else {\n    throw new Error('No valid JSON found in AI response');\n  }\n}\n\n// Validate presentation structure\nif (!presentation.sections || !Array.isArray(presentation.sections)) {\n  throw new Error('Invalid presentation structure: missing sections array');\n}\n\nif (presentation.sections.length !== 7) {\n  throw new Error(`Invalid section count: expected 7, got ${presentation.sections.length}`);\n}\n\n// Ensure all sections have required fields and calculate stats\nlet totalDuration = 0;\nlet storyCount = 0;\n\npresentation.sections = presentation.sections.map((section, index) => {\n  // Count stories in success stories section\n  if (section.title?.toLowerCase().includes('success') || section.title?.toLowerCase().includes('stories')) {\n    // Count stories by looking for story indicators\n    const storyIndicators = section.script.match(/client|customer|worked with|helped|story|example/gi);\n    storyCount = Math.min(storyIndicators ? Math.ceil(storyIndicators.length / 3) : 2, 3);\n  }\n  \n  const duration = section.duration || 60;\n  totalDuration += duration;\n  \n  return {\n    id: section.id || index + 1,\n    title: section.title || `Section ${index + 1}`,\n    timing: section.timing || `${Math.floor(totalDuration/60)}:${(totalDuration%60).toString().padStart(2,'0')}`,\n    duration: duration,\n    description: section.description || '',\n    script: section.script || '',\n    notes: section.notes || ''\n  };\n});\n\n// Format duration as MM:SS\nconst formatDuration = (seconds) => {\n  const mins = Math.floor(seconds / 60);\n  const secs = seconds % 60;\n  return `${mins}:${secs.toString().padStart(2, '0')}`;\n};\n\n// Create the final response structure\nconst finalResponse = {\n  success: true,\n  presentation: {\n    title: presentation.title || 'BNI Feature Presentation',\n    sections: presentation.sections,\n    metadata: {\n      duration: formatDuration(totalDuration),\n      totalSeconds: totalDuration,\n      sectionCount: presentation.sections.length,\n      storyCount: storyCount,\n      framework: presentation.metadata?.framework || context.presentationContext.presentationGoal.framework,\n      goal: presentation.metadata?.goal || context.presentationContext.presentationGoal.type,\n      generatedAt: new Date().toISOString()\n    }\n  }\n};\n\nreturn {\n  json: finalResponse\n};"}, "name": "Structure Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1056, -100], "id": "030c2482-79d4-47bb-bea5-33e8f1b70ce2"}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}", "options": {"responseCode": 200, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}]}}}, "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1276, -100], "id": "b90867e8-52ec-4560-9580-85379092ba73"}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"error\": \"{{ $json.error.message || 'An error occurred during presentation generation' }}\",\n  \"code\": \"{{ $json.error.code || 'GENERATION_ERROR' }}\",\n  \"timestamp\": \"{{ new Date().toISOString() }}\"\n}", "options": {"responseCode": 500, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}]}}}, "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 260], "id": "30a0cc79-ba07-495f-891d-06c33cc744f4"}, {"parameters": {"jsCode": "// Catch and format any errors\nconst error = $input.item.error || {};\nconst errorMessage = error.message || 'Unknown error occurred';\nconst errorCode = error.name || 'UNKNOWN_ERROR';\n\nconsole.error('Workflow Error:', {\n  message: errorMessage,\n  code: errorCode,\n  stack: error.stack,\n  timestamp: new Date().toISOString()\n});\n\n// Determine error type and user-friendly message\nlet userMessage = 'An error occurred while generating your presentation';\nlet statusCode = 500;\n\nif (errorMessage.includes('Missing required field')) {\n  userMessage = errorMessage;\n  statusCode = 400;\n} else if (errorMessage.includes('timeout')) {\n  userMessage = 'The presentation generation timed out. Please try again.';\n  statusCode = 504;\n} else if (errorMessage.includes('AI') || errorMessage.includes('OpenRouter')) {\n  userMessage = 'The AI service is temporarily unavailable. Please try again in a moment.';\n  statusCode = 503;\n}\n\nreturn {\n  json: {\n    error: {\n      message: userMessage,\n      code: errorCode,\n      details: errorMessage,\n      statusCode: statusCode\n    }\n  }\n};"}, "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [240, 260], "id": "6f9d3984-77f2-4cf5-a927-7605f8910c56"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [880, 120], "id": "e707d5df-adbd-433f-b086-00109c955b19", "name": "Structured Output Parser"}], "connections": {"Presentation Webhook": {"main": [[{"node": "Validate & Structure Data", "type": "main", "index": 0}]]}, "Validate & Structure Data": {"main": [[{"node": "Generate Presentation with AI", "type": "main", "index": 0}]]}, "Generate Presentation with AI": {"main": [[{"node": "Structure Response", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "Generate Presentation with AI", "type": "ai_languageModel", "index": 0}]]}, "Structure Response": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Error Handler": {"main": [[{"node": "Error Response", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Generate Presentation with AI", "type": "ai_outputParser", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "c972fa4e-c600-4e35-b09c-f44f43a5c57c", "triggerCount": 1, "shared": [{"createdAt": "2025-06-11T12:03:14.686Z", "updatedAt": "2025-06-11T12:03:14.686Z", "role": "workflow:owner", "workflowId": "ksX2FAoyw15Tq2Kn", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}