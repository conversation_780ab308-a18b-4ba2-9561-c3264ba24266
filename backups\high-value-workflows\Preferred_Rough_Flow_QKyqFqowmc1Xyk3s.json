{"createdAt": "2025-06-07T22:05:11.121Z", "updatedAt": "2025-06-07T22:29:55.000Z", "id": "QKyqFqowmc1Xyk3s", "name": "My preferred Rough floow.", "active": false, "isArchived": false, "nodes": [{"parameters": {"path": "5fab1a9b-56e2-4f04-aa1d-7d4277e2370b", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-220, 0], "id": "c2207629-90dd-4e84-8abb-f9bfd41a0614", "name": "Webhook", "webhookId": "5fab1a9b-56e2-4f04-aa1d-7d4277e2370b"}, {"parameters": {"options": {"systemMessage": "You are a helpful assistant"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [220, 0], "id": "********-bdce-44bb-8683-f31adada6c58", "name": "AI Agent"}, {"parameters": {"model": "deepseek/deepseek-chat", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [120, 240], "id": "371eb5bd-5bdd-4a0e-8760-cff59426cb4e", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "yn3czriQEae3D6kG", "name": "OpenRouter account"}}}, {"parameters": {"operation": "update"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [720, 0], "id": "083a0466-6717-409d-ae43-ca0d691b883e", "name": "Supabase", "credentials": {"supabaseApi": {"id": "MaYgyPFsCO1qMHKL", "name": "Supabase account"}}}, {"parameters": {}, "type": "n8n-nodes-base.supabaseTool", "typeVersion": 1, "position": [380, 220], "id": "19cae891-1bdf-4144-aa4f-7bd65008e6ae", "name": "Supabase1", "credentials": {"supabaseApi": {"id": "MaYgyPFsCO1qMHKL", "name": "Supabase account"}}}], "connections": {"Webhook": {"main": [[]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Supabase", "type": "main", "index": 0}]]}, "Supabase1": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "66e6f3eb-ef84-4a66-be9a-bfb27801f8d8", "triggerCount": 0, "shared": [{"createdAt": "2025-06-07T22:05:11.125Z", "updatedAt": "2025-06-07T22:05:11.125Z", "role": "workflow:owner", "workflowId": "QKyqFqowmc1Xyk3s", "projectId": "jncwk4CjUI6FYXvX"}], "tags": []}