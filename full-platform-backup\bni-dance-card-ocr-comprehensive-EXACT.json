{"createdAt": "2025-06-09T09:08:29.468Z", "updatedAt": "2025-06-13T12:23:22.000Z", "id": "XJDEslazu5PowwxK", "name": "BNI Dance Card OCR - Comprehensive Fields", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "bni-dance-card-ocr-comprehensive", "responseMode": "responseNode", "options": {"allowedOrigins": "https://bni-presentation-generator.netlify.app,http://localhost:5173,http://localhost:5174,http://localhost:3000,http://localhost:4173"}}, "name": "Dance Card Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [220, 340], "id": "webhook-node", "webhookId": "bni-dance-card-ocr-comprehensive"}, {"parameters": {"jsCode": "// Comprehensive validation and data preparation for BNI Dance Card OCR\nconst payload = $input.item.json.body || $input.item.json;\n\nconsole.log(\"=== BNI DANCE CARD OCR - COMPREHENSIVE FIELDS ===\");\nconsole.log(\"Timestamp:\", new Date().toISOString());\nconsole.log(\"Payload:\", JSON.stringify(payload, null, 2));\n\n// Initialize variables\nlet document_url;\nlet uploadId;\nlet filename = \"dance-card\"; // Default filename\n\n// Check for document URL in multiple possible locations\nif (payload.dance_card_url) {\n  document_url = payload.dance_card_url;\n  uploadId = payload.uploadId;\n  console.log(\"Processing presentation dance card:\", document_url);\n} else if (payload.document_url) {\n  document_url = payload.document_url;\n  uploadId = payload.uploadId || null;\n} else {\n  throw new Error(\"Missing required field: document_url or dance_card_url\");\n}\n\n// Extract filename with robust URL parsing\ntry {\n  const urlObj = new URL(document_url);\n  const pathParts = urlObj.pathname.split(\"/\");\n  const lastSegment = pathParts[pathParts.length - 1];\n  \n  if (lastSegment) {\n    // Remove query parameters and decode URI\n    filename = decodeURIComponent(lastSegment.split(\"?\")[0]);\n  }\n} catch (e) {\n  console.warn(\"Could not parse URL, attempting basic extraction:\", e.message);\n  // Fallback: basic string manipulation\n  const urlParts = document_url.split(\"/\");\n  const lastPart = urlParts[urlParts.length - 1];\n  if (lastPart) {\n    filename = lastPart.split(\"?\")[0];\n  }\n}\n\n// Extract and validate file extension\nconst fileExtension = filename.split(\".\").pop().toLowerCase();\nconst supportedTypes = [\"pdf\", \"jpg\", \"jpeg\", \"png\", \"docx\", \"pptx\"];\n\nif (!supportedTypes.includes(fileExtension)) {\n  throw new Error(`Unsupported file type: ${fileExtension}. Supported types: ${supportedTypes.join(\", \")}`);\n}\n\n// Determine file type category\nconst fileType = fileExtension === \"pdf\" ? \"pdf\" : \n                [\"jpg\", \"jpeg\", \"png\"].includes(fileExtension) ? \"image\" : \n                \"document\";\n\n// Prepare output data\nreturn {\n  json: {\n    document_url: document_url,\n    upload_id: uploadId,\n    filename: filename,\n    file_extension: fileExtension,\n    file_type: fileType,\n    timestamp: new Date().toISOString(),\n    original_payload: payload,\n    // Metadata for tracking\n    processing_metadata: {\n      workflow: \"bni-dance-card-ocr-comprehensive\",\n      version: \"3.0\",\n      environment: \"production\"\n    }\n  }\n};"}, "name": "Validate & Prepare Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, 340], "id": "validate-node"}], "connections": {"Dance Card Webhook": {"main": [[{"node": "Validate & Prepare Data", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": null, "pinData": {}, "versionId": "8bf131f3-f012-4654-932f-e994c784ee1e", "triggerCount": 1, "backup_metadata": {"backup_status": "EXACT_REPLICA_FROM_N8N", "backup_timestamp": "2025-01-03T00:00:00Z", "source": "n8n_mcp_get_workflow", "workflow_complexity": "very_high", "node_count": 14, "workflow_type": "comprehensive_ocr_processing", "crash_status": "fixable_timeout_issue", "comprehensive_fields": 35}}