{"createdAt": "2025-06-08T21:53:09.103Z", "updatedAt": "2025-06-09T14:31:26.000Z", "id": "D2dSFnvKxbXdLxj6", "name": "BNI Dance Card OCR - Production", "active": false, "isArchived": false, "workflow_summary": {"description": "Production-ready BNI Dance Card OCR workflow using Mistral OCR and OpenRouter LLM", "key_features": ["Webhook endpoint for document processing", "Mistral OCR for text extraction", "OpenRouter LLM for information extraction", "Comprehensive error handling and validation", "Structured member data extraction", "Production-ready with CORS support"], "main_components": ["Document webhook with CORS", "Data validation and preparation", "Document download and processing", "Mistral OCR integration", "Information extraction with LLM", "Structured response formatting"], "extracted_data_fields": ["chapter_name", "chapter_location", "meeting_day", "meeting_time", "meeting_date", "members array with business details", "referral statistics"], "integrations": ["Mistral OCR API", "OpenRouter API (DeepSeek model)", "Webhook endpoints"], "note": "This is a production workflow for processing BNI dance cards with comprehensive error handling and structured data extraction."}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": null, "pinData": {}, "versionId": "29f49aaa-9aea-4fc4-bea8-5819a78da73b", "triggerCount": 1, "shared": [{"createdAt": "2025-06-08T21:53:09.104Z", "updatedAt": "2025-06-08T21:53:09.104Z", "role": "workflow:owner", "workflowId": "D2dSFnvKxbXdLxj6", "projectId": "jncwk4CjUI6FYXvX"}], "tags": []}