{"createdAt": "2025-05-31T14:16:43.019Z", "updatedAt": "2025-06-07T23:58:33.000Z", "id": "tJa1bYVc2KqEtmkd", "name": "LinkedIn SAAS - Production Ready v2", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "8da077f9-612d-438e-8813-72694186d247", "options": {"allowedOrigins": "*", "responseHeaders": {"entries": [{"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Methods", "value": "POST, OPTIONS"}, {"name": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, Accept"}, {"name": "Access-Control-Max-Age", "value": "86400"}]}}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [840, 120], "id": "webhook-trigger", "name": "Webhook", "webhookId": "ad9cabea-f74d-4127-9a7d-89bf53606f28"}, {"parameters": {"toolDescription": "Use this tool to search the web for current information.", "method": "POST", "url": "https://api.tavily.com/search", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"query\": \"{searchTerm}\",\n  \"topic\": \"general\",\n  \"search_depth\": \"advanced\",\n  \"chunks_per_source\": 3,\n  \"max_results\": 2,\n  \"time_range\": null,\n  \"days\": 7,\n  \"include_answer\": true,\n  \"include_raw_content\": false,\n  \"include_images\": false,\n  \"include_image_descriptions\": false,\n  \"include_domains\": [],\n  \"exclude_domains\": []\n}", "placeholderDefinitions": {"values": [{"name": "searchTerm", "description": "What the user is searching for."}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1120, 380], "id": "tavily-search", "name": "<PERSON><PERSON>", "credentials": {"httpHeaderAuth": {"id": "R8n5b7bT7SZKoXq0", "name": "<PERSON><PERSON>"}}}, {"parameters": {"model": "openai/gpt-4.1", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [1260, 380], "id": "gpt-model", "name": "GPT-4.1", "credentials": {"openRouterApi": {"id": "yn3czriQEae3D6kG", "name": "OpenRouter account"}}}, {"parameters": {"promptType": "define", "text": "=Topic of Post: {{ $json.body['Topic of Post'] }}\n\nTarget Audience: {{ $json.body['Target Audience'] }}", "options": {"systemMessage": "# LinkedIn Post Creator for Brand Wisdom Solutions\n\nYou are an AI agent specialized in creating professional, engaging LinkedIn posts for <PERSON><PERSON><PERSON><PERSON>, Founder & CEO of Brand Wisdom Solutions.\n\n## Company Context:\n- Brand Wisdom Solutions: Full-service branding and digital marketing agency in Pune, India\n- Expertise: Brand Management, AI-powered Marketing, Business Process Automation\n- Philosophy: \"Wise Brand\" framework - trust, strategic insight, market distinction\n- Track Record: 200+ successful projects, Digital Excellence Awards recognition\n- Approach: Data-driven, value-first, client-centric with measurable ROI\n\n## Instructions:\n1. ALWAYS start by using the Tavily search tool to research the topic\n2. Create a LinkedIn post that:\n   - Opens with an engaging hook or insight\n   - Maintains professional yet conversational tone\n   - Provides actionable value to readers\n   - Incorporates Brand Wisdom Solutions' perspective when relevant\n   - Uses data/examples from your research\n   - Includes 3-5 relevant hashtags\n   - Ends with a question or call-to-action\n   - Stays between 150-300 words\n\n## Output Format:\nReturn ONLY the final LinkedIn post text, nothing else. Write from <PERSON><PERSON><PERSON><PERSON>'s perspective as the agency leader.\n\n## Example Structure:\n[Hook/Insight]\n[Main content with value]\n[Brand Wisdom perspective if relevant]\n[Call to action/question]\n[Hashtags]"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1100, 100], "id": "linkedin-post-creator", "name": "LinkedIn Post Creator"}], "connections": {"Webhook": {"main": [[{"node": "LinkedIn Post Creator", "type": "main", "index": 0}]]}, "Tavily Search": {"ai_tool": [[{"node": "LinkedIn Post Creator", "type": "ai_tool", "index": 0}]]}, "GPT-4.1": {"ai_languageModel": [[{"node": "LinkedIn Post Creator", "type": "ai_languageModel", "index": 0}, {"node": "Image Prompt Generator", "type": "ai_languageModel", "index": 0}]]}, "LinkedIn Post Creator": {"main": [[{"node": "Image Prompt Generator", "type": "main", "index": 0}]]}, "Image Prompt Generator": {"main": [[{"node": "DALL-E Image Generator", "type": "main", "index": 0}]]}, "DALL-E Image Generator": {"main": [[{"node": "Convert to Image", "type": "main", "index": 0}]]}, "Convert to Image": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "Send Email": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "4a1da99b-158b-4209-9ed4-5e9a7df327b7", "triggerCount": 1, "shared": [{"createdAt": "2025-05-31T14:16:43.021Z", "updatedAt": "2025-05-31T14:16:43.021Z", "role": "workflow:owner", "workflowId": "tJa1bYVc2KqEtmkd", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}