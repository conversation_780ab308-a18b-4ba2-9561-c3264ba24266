{"createdAt": "2025-06-13T12:24:18.938Z", "updatedAt": "2025-06-13T12:25:10.000Z", "id": "6xzd7MbVuHV0299h", "name": "AI Asset Generator in n8n", "active": false, "isArchived": false, "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-1100, 80], "id": "afc3ab0d-949b-436d-9fe4-2b04f5821492", "name": "When clicking 'Execute workflow'"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/jljBwyyQakqrL1wae/run-sync-get-dataset-items", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Authorization", "value": "Bearer <yourTokenHere>"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"getPersonalEmails\": true,\n    \"getWorkEmails\": true,\n    \"totalRecords\": 500,\n    \"url\": \"https://app.apollo.io/#/people?page=1&personTitles[]=founder&personTitles[]=partner&personTitles[]=cofounder&qKeywords=creative%20agency&sortAscending=false&sortByField=%5Bnone%5D\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-880, 80], "id": "34c51ec5-b2a9-4224-b63d-01aae212d654", "name": "Run Apify Actor & Get Results"}, {"parameters": {"url": "={{ $json.organization.website_url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 0], "id": "4e70b3a2-1d28-48c8-b05b-36b9cf052031", "name": "HTTP Request"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "0b87a017-e022-45bf-90f2-09ae9439424b", "leftValue": "={{ $json.organization.website_url }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}, {"id": "87b56625-b947-4154-acb0-a3d1583970c9", "leftValue": "={{ $json.email }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-660, 80], "id": "86f6e22a-9a87-4b8c-afb1-e3858c33dc01", "name": "Filter"}, {"parameters": {"maxItems": 10, "keep": "lastItems"}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [-440, 80], "id": "0ebbba57-ff2f-4278-b008-efbb247f05b1", "name": "Limit"}, {"parameters": {"html": "={{ $json.data }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [220, 0], "id": "48706aff-e693-4085-8006-f5b000478a54", "name": "<PERSON><PERSON>"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "GPT-4.1"}, "messages": {"values": [{"content": "You are a helpful, intelligent assistant.", "role": "system"}, {"content": "Your task is to take as input a bunch of unstructured information about a person's website, and their LinkedIn profiles, and then convert this into the following JSON object:\n\n{\"websiteContext\":\"\",\"personContext\":\"\",\"uniqueAngles\":\"\"}\n\n--\n\nYou'll receive all of the data you need in an unstructured string. Your task is to parse that out and turn that into the above object.\n\nRules:\n1. Go deep into detail for websiteContext. Write at least two paragraphs.\n2. For personContext, use all of the data available.\n3. For uniqueAngles, use the website data and the information about the provided person to create three \"interesting points\" that we could write about in a later article.\n4. Return any newlines as \\n"}, {"content": "=Website scrape:\n\n{{ $json.data }}\n\n--\n\nPersonal information:\n{{ $('Limit').item.json.name }}\n{{ $('Limit').item.json.title }}\n{{ $('Limit').item.json.headline }}\n{{ $('Limit').item.json.email }}\n{{ $('Limit').item.json.state }}\n{{ $('Limit').item.json.city }}\n{{ $('Limit').item.json.country }}\n{{ $('Limit').item.json.organization.name }}\n{{ $('Limit').item.json.organization.website_url }}\n{{ $('Limit').item.json.seniority }}\n{{ $('Limit').item.json.industry }}\nemployees: {{ $('Limit').item.json.estimated_num_employees }}"}]}, "jsonOutput": true, "options": {"temperature": 0.6}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [440, 0], "id": "4e3c9bfa-6595-49d1-b7a5-5fbef4bc8525", "name": "Generate Context"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "GPT-4.1"}, "messages": {"values": [{"content": "You are a helpful, intelligent assistant.", "role": "system"}, {"content": "Your task is to take as input information about a website & a person, and then return a customized, polished newsletter that I will use as a lead magnet to sell them on stuff.\n\nYou'll receive your data in JSON using the following format:\n\n{\"websiteContext\":\"\",\"personContext\":\"\",\"uniqueAngles\":\"\"}\n\nTo write your newsletter, you'll be using the following example.\n\n--\n\nOur knowledge economy is swiftly coming to an end\n\nPlus: will ChatGPT kill Google Search?\n\nWelcome to The Cusp: cutting-edge AI news (and its implications) explained in simple English.\n\nIn this week's issue:\n\n1. ChatGPT (or software like it) poised to disrupt Google search,\n2. Whisper V2 quietly launched in the background,\n3. Our knowledge economy is coming to an end: how to insulate yourself against the coming rapid changes in AI capability\n\nLet's dive in.\n\n1. ChatGPT poised to disrupt Google Search\n\nIf you're reading this, you've probably already heard of ChatGPT.\n\nBut for those that haven't: ChatGPT is an incredible dialog agent unveiled by OpenAI last week. It's been trained, reinforced, and optimized for one specific purpose: to assist the hell out of you. And it does the job incredibly well.\n\nIn just seven days, people have already begun using it to outsource their programming jobs, education, and even customer service.\n\nHowever: the real game-changer here is its potential to decimate Google search.\n\nChatGPT's ability to answer questions, interweave context, and provide detailed code examples make it likely to become the go-to for info seekers in the near future.\n\nThe problem with Google\n\nGoogle has dominated the search space for over two decades, and its massive market share invariably gives it a huge advantage. But in recent years, their search results page has grown to resemble more of a spam marketplace than a knowledge engine.\n\nSearches are noisy, stuffed with irrelevant keywords, and ultimately leave users disappointed more often than not.\n\nIn contrast, ChatGPT provides users with concise and detailed answers to their queries in a fraction of the time. It uses context, factors in both the reading & writing level of the person who's speaking to it, and (unlike a search engine) can relate concepts to one another.\n\nInstead of general content or spammy links, users get an actual response to their query, and all of it is presented in the most natural way possible to us lowly humans: a back-and-forth conversation.\n\nHow can we take advantage of it?\n\nChatGPT is, of course, a closed-source implementation. People have created mock APIs that use browser automation to send messages elsewhere, but frankly I think that's missing the picture.\n\nThe real power of ChatGPT isn't in its direct economic utility (as in, how can I spin it to make more money), but in its utility as an educational force multiplier.\n\nChatGPT really is an assistant. The best way to derive its value is to use it as such. I've had ChatGPT help me mock up Vue boilerplate, ideate content for blog posts (like this one), and even explain complicated programming concepts using metaphors.\n\nSome more ideas:\n\n- Paste in a new codebase or API & have it explain, line-by-line, how it works.\nSelf-teach history, politics, game theory, rationality—whatever you want.\n- Create lists of metaphors for common, but hard-to-explain topics. Memorize them to use with your friends, and consistently wow them with how smart you are\nIf I were to guess, it makes you anywhere from 50% to 100% more productive. Think of it like having a teacher in your pocket.\n\nAnd OpenAI has plans to give it internet access, which would increase its assistance capabilities tenfold. I can't wait!\n\n2. Whisper V2 quietly launched\n\nWhisper, the world's most powerful speech transcription model, made its debut a little over two months ago.\n\nSince then, people have used it to automatically transcribe lectures, Twitter videos, and more with >99% accuracy.\n\nBut OpenAI quietly pushed a change to their most capable model yesterday that improved it even further.\n\nHow can we take advantage of this?\nYou know how YouTube captions get ~80% of the way there?\n\nWhisper is several orders of magnitude better.\n\nAnd because it's open source, you can use this model in your own projects relatively easily.\n\nYou could:\n\n1. Create a simple Whisper API in Flask, compile a list of transcription services, & cold email them to offer a free implementation. Charge per usage.\n2. Hook Whisper up to a police or radio scanner. Make a Twitter account and post noteworthy announcements on a regional basis automatically with the Twitter API.\n3. Transcribe noisy, low-quality lectures from the 1950s-1980s (think similar to the Feynman series) and bundle them into a book series.\n\n3. Our knowledge economy is coming to an end\n\nFor the last sixty-odd years, our economy has depended on human intelligence to create and maintain economic value. That phase of history is (fortunately or unfortunately) coming to an end.\n\nMost digital-only companies—especially those that work in creative verticals—will face massive disruption in the next few years, unless they make artificial intelligence their explicit focus.\n\nAny company that doesn't pivot to AI won't stand a chance at developing better models than the big players (OpenAI, DeepMind, Google, etc). If that describes you, the quality of your service will eventually be swallowed up by increasing AI capabilities, rendering it moot.\n\nProgramming, marketing, design, education, animation, forecasting, financial planning, research, music... really, any industry whose main output is creative association/knowledge will be run primarily by APIs that a handful of scale-oriented companies develop.\n\nHow do we take advantage of this?\n\nMost companies aren't going to be able to successfully pivot to AI. You need hundreds of millions of dollars, extremely talented engineers, and sizeable compute infrastructure (or the means to procure it).\n\nA small room in one of Microsoft's billion-dollar data warehouses.\nSo for everyone else: in order to win, you need to stop focusing on where the puck is, and begin skating to where the puck is going to be.\n\nWhat might this look like?\n\n1. Focus on interfaces, not infrastructure. Since any functionality worth having will be an API call away, your organization's key differentiator will be the quality of your customer experience & its overall operational efficiency—not how well your team can fulfill x, y, or z service.\n2. Insulate your product from rapid advancements in AI. Tie it into the physical world, where AI will have less of an impact this decade. Pivot into the conference space, for example, or add a physical product to your offering. Your goal should be to increase the number of real-world dependencies and relationships, since those are not easily automatable.\n3. Spend time and money on personal branding. Join the public discourse (if you haven't already). Ensure a sizeable number of stakeholders follow you, not your business—so that when the latter is invariably overtaken by technology, you still have something left.\n\nAnyone reading this still has some runway, of course. But the length of that runway depends on uncertain factors, like the quality of your relationships with your customers, the amount of exposure they have to jaw-droppingly powerful AI, etc.\n\nBetter be safe than sorry.\n\nThat's a wrap!\n\nEnjoyed this? Consider sharing with someone you know. And if you're reading this because someone sent it to you, get the next newsletter by signing up here. You can also follow me on Twitter if you'd prefer a shorter format.\n\nSee you next week.\n\n– {thePersonsName}\n\n--\n\nReturn the entire newsletter in Markdown (atx) format using this JSON:\n\n{\"title\":\"\",\"subheading\":\"\",\"newsletterBody\":\"\"}"}, {"content": "={\"websiteContext\":\"{{ $json.message.content.websiteContext }}\",\"personContext\":\"{{ $json.message.content.personContext }}\",\"uniqueAngles\":\"{{ $json.message.content.uniqueAngles }}\"}"}]}, "jsonOutput": true, "options": {"temperature": 0.6}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [820, 0], "id": "dcfa5e29-d1c9-459f-bc9c-939e4552f694", "name": "Generate Newsletter"}, {"parameters": {"mode": "markdownToHtml", "markdown": "={{ $json.message.content.newsletterBody }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [1200, 0], "id": "96de1116-0801-485e-ab2b-33ba55d91b1e", "name": "Markdown1"}, {"parameters": {"folderId": "default", "title": "=Newsletter for {{ $('Limit').item.json.first_name }}"}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [1420, 0], "id": "44fbee88-9117-4ef5-b345-580e8b40d9a1", "name": "Google Docs"}, {"parameters": {"method": "PATCH", "url": "=https://www.googleapis.com/upload/drive/v3/files/{{ $json.id }}?uploadType=media", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDocsOAuth2Api", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "{\n  \"Content-Type\":\"text/html\"\n}", "sendBody": true, "contentType": "raw", "rawContentType": "text/html", "body": "={{ $('Markdown1').item.json.data }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1640, 0], "id": "7821a509-e4b5-4cb3-8d30-e71000b12e8b", "name": "HTTP Request1"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-220, 80], "id": "3a69b277-6d07-4ff8-a32f-c247748c4c9a", "name": "Loop Over Items"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1860, 80], "id": "22929d2f-20c1-4440-b6fa-51f7488e826e", "name": "Wait", "webhookId": "cf025241-20d7-4fb9-93aa-a660b5c58820"}], "connections": {"When clicking 'Execute workflow'": {"main": [[{"node": "Run Apify Actor & Get Results", "type": "main", "index": 0}]]}, "Run Apify Actor & Get Results": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Markdown": {"main": [[{"node": "Generate Context", "type": "main", "index": 0}]]}, "Generate Context": {"main": [[{"node": "Generate Newsletter", "type": "main", "index": 0}]]}, "Generate Newsletter": {"main": [[{"node": "Markdown1", "type": "main", "index": 0}]]}, "Markdown1": {"main": [[{"node": "Google Docs", "type": "main", "index": 0}]]}, "Google Docs": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": null, "pinData": {}, "versionId": "611d02ae-8f45-47ec-b98b-008fc31e8f35", "triggerCount": 0, "shared": [{"createdAt": "2025-06-13T12:24:18.945Z", "updatedAt": "2025-06-13T12:24:18.945Z", "role": "workflow:owner", "workflowId": "6xzd7MbVuHV0299h", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}