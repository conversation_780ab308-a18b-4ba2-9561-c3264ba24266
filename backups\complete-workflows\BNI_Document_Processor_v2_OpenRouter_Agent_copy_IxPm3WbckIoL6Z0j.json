{"createdAt": "2025-06-08T12:59:24.045Z", "updatedAt": "2025-06-08T12:59:24.045Z", "id": "IxPm3WbckIoL6Z0j", "name": "BNI Document Processor v2 - OpenRouter Agent copy", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "c33fdadc-18be-4c1c-9a0f-49f1ca9fb97f", "responseMode": "responseNode", "options": {"allowedOrigins": "https://bni-presentation-generator.netlify.app,http://localhost:5173,http://localhost:5174,http://localhost:3000,http://localhost:4173"}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 320], "id": "b6eb9330-bd83-45e8-a299-48f01e99ceb6", "name": "Document Webhook", "webhookId": "c33fdadc-18be-4c1c-9a0f-49f1ca9fb97f"}, {"parameters": {"jsCode": "// Validate and log incoming data\nconst payload = $input.item.json.body || $input.item.json;\n\nconsole.log('=== DOCUMENT PROCESSING REQUEST ===');\nconsole.log('Timestamp:', new Date().toISOString());\nconsole.log('Payload:', JSON.stringify(payload, null, 2));\n\n// Validate required fields\nif (!payload.document_url) {\n  throw new Error('Missing required field: document_url');\n}\n\n// Extract file info\nconst url = payload.document_url;\nconst fileExtension = url.split('.').pop().toLowerCase();\nconst isPDF = fileExtension === 'pdf';\nconst isImage = ['jpg', 'jpeg', 'png'].includes(fileExtension);\n\nif (!isPDF && !isImage) {\n  throw new Error(`Unsupported file type: ${fileExtension}`);\n}\n\nreturn {\n  json: {\n    document_url: payload.document_url,\n    presentation_id: payload.presentation_id || null,\n    file_type: isPDF ? 'pdf' : 'image',\n    file_extension: fileExtension,\n    timestamp: new Date().toISOString(),\n    original_payload: payload\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 320], "id": "bce6e89d-5705-4eac-bd4e-1e0459ee2fee", "name": "Validate & Prepare Data"}, {"parameters": {"url": "={{ $json.document_url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 320], "id": "68c9cd3f-dbe7-4b79-9405-0d6d858a65a9", "name": "Download Document"}, {"parameters": {"promptType": "define", "text": "={{ $json }}", "options": {"systemMessage": "You are an expert at analyzing BNI dance cards and member lists. Extract all member information and analyze networking opportunities. Return JSON with members, potential partnerships, and observations.", "maxIterations": 1}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [900, 320], "id": "31080e55-1139-4ca0-8bb3-9f5bd98b7253", "name": "BNI Document Analyzer"}, {"parameters": {"options": {"maxTokens": 2000, "temperature": 0.3}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [760, 540], "id": "a92a73ba-7998-4ea6-94a0-c5e834177183", "name": "OpenRouter Model", "credentials": {"openRouterApi": {"id": "yn3czriQEae3D6kG", "name": "OpenRouter account"}}}, {"parameters": {"jsCode": "// Parse and structure the AI analysis results\nconst aiResponse = $input.item.json;\nconst originalData = $('Validate & Prepare Data').item.json;\n\nconsole.log('AI Response:', JSON.stringify(aiResponse, null, 2));\n\ntry {\n  let analysisContent = aiResponse.output || aiResponse.text || aiResponse;\n  \n  // Try to parse if it's a string\n  if (typeof analysisContent === 'string') {\n    const jsonMatch = analysisContent.match(/\\{[\\s\\S]*\\}/);\n    if (jsonMatch) {\n      analysisContent = JSON.parse(jsonMatch[0]);\n    }\n  }\n\n  return {\n    json: {\n      success: true,\n      presentation_id: originalData.presentation_id,\n      document_url: originalData.document_url,\n      file_type: originalData.file_type,\n      extraction_data: analysisContent,\n      processed_at: new Date().toISOString()\n    }\n  };\n} catch (error) {\n  return {\n    json: {\n      success: false,\n      error: error.message,\n      raw_response: aiResponse\n    }\n  };\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1200, 320], "id": "6bf2deb0-439c-4cf2-a7ca-8c2879784419", "name": "Structure Results"}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {"responseCode": 200, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}]}}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1420, 320], "id": "0424d97f-3fb6-4269-9b76-330bfc8876fe", "name": "Respond to Webhook"}], "connections": {"Document Webhook": {"main": [[{"node": "Validate & Prepare Data", "type": "main", "index": 0}]]}, "Validate & Prepare Data": {"main": [[{"node": "Download Document", "type": "main", "index": 0}]]}, "Download Document": {"main": [[{"node": "BNI Document Analyzer", "type": "main", "index": 0}]]}, "BNI Document Analyzer": {"main": [[{"node": "Structure Results", "type": "main", "index": 0}]]}, "OpenRouter Model": {"ai_languageModel": [[{"node": "BNI Document Analyzer", "type": "ai_languageModel", "index": 0}]]}, "Structure Results": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "1ce06830-d5b9-4533-84b6-97e2e0fb85b0", "triggerCount": 1, "shared": [{"createdAt": "2025-06-08T12:59:24.049Z", "updatedAt": "2025-06-08T12:59:24.049Z", "role": "workflow:owner", "workflowId": "IxPm3WbckIoL6Z0j", "projectId": "jncwk4CjUI6FYXvX"}], "tags": []}