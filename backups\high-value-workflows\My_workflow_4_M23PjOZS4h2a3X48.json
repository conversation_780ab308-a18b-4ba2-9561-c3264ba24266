{"createdAt": "2025-06-30T16:58:04.503Z", "updatedAt": "2025-06-30T16:58:04.503Z", "id": "M23PjOZS4h2a3X48", "name": "My workflow 4", "active": false, "isArchived": false, "nodes": [{"parameters": {"model": "gpt-4o", "options": {"frequencyPenalty": 0.2, "temperature": 0.7}}, "id": "3b6dc0b7-9671-4c0e-b428-ac176ad0a576", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [280, 240], "typeVersion": 1}, {"parameters": {"sessionKey": "=chat_with_{{ $('Listen for incoming events').first().json.message.chat.id }}", "contextWindowLength": 10}, "id": "3711ee2d-41d2-47e0-b535-590d92c679be", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [400, 240], "typeVersion": 1}, {"parameters": {"chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "text": "={{ $('AI Agent').item.json.output.replace(/&/g, \"&amp;\").replace(/>/g, \"&gt;\").replace(/</g, \"&lt;\").replace(/\"/g, \"&quot;\") }}", "additionalFields": {"appendAttribution": false, "parse_mode": "HTML"}}, "id": "7372d2c3-b7e0-4777-a489-f445b8e7dddb", "name": "Correct errors", "type": "n8n-nodes-base.telegram", "position": [860, 20], "typeVersion": 1.1, "webhookId": "6b0804c3-e904-4289-8198-76fa21678cfa"}, {"parameters": {"updates": ["*"], "additionalFields": {}}, "id": "5494700c-c070-423d-8300-bd7306fecda6", "name": "Listen for incoming events", "type": "n8n-nodes-base.telegramTrigger", "position": [-840, -220], "webhookId": "322dce18-f93e-4f86-b9b1-3305519b7834", "typeVersion": 1}, {"parameters": {"resource": "file", "fileId": "={{$json.message.voice.file_id}}"}, "id": "45b4e90f-a19e-45a9-8a94-64d61422cbd8", "name": "Download voice file", "type": "n8n-nodes-base.telegram", "position": [-400, 20], "typeVersion": 1.2, "webhookId": "ad4e61ea-4d2e-426e-8b5e-2f98ca8f1064"}, {"parameters": {"assignments": {"assignments": [{"id": "bccbce0a-7786-49c9-979a-7a285cb69f78", "name": "CombinedMessage", "type": "string", "value": "={{ $json.message && $json.message.text ? $json.message.text : ($json.text ? $json.text : '') }}"}, {"id": "5b1fc9f5-1408-4099-88cc-a23725c9eddb", "name": "Message Type ", "type": "string", "value": "={{ $json?.message?.text && !$json?.text ? \"text query\" : (!$json?.message?.text && $json?.text ? \"voice message\" : \"unknown type message\") }}"}, {"id": "1e9a17fa-ec5d-49dc-9ff6-1f28b57fb02e", "name": "Source Type", "type": "string", "value": "={{ $('Listen for incoming events').item.json.message.forward_origin ? \" forwarded\" : \"\" }}"}]}, "options": {}}, "id": "110a9b92-ea10-4ff7-8426-89ea28dd4009", "name": "Combine content and set properties", "type": "n8n-nodes-base.set", "position": [40, 20], "typeVersion": 3.4}, {"parameters": {"chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "text": "={{ $json.output }} \n\nThank you for your{{ $('Combine content and set properties').item.json['Source Type'] }} {{ $('Combine content and set properties').item.json['Message Type '] }} 🤗", "additionalFields": {"appendAttribution": false, "parse_mode": "HTML"}}, "id": "d4dc07cc-c4e6-470c-aa9d-a243c2b3ad48", "name": "Send final reply", "type": "n8n-nodes-base.telegram", "position": [640, 20], "typeVersion": 1.1, "webhookId": "9450e26d-63ee-4f8d-a7dd-fd96d9c2fa61", "onError": "continueErrorOutput"}, {"parameters": {"chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "text": "=Sorry, {{ $('Listen for incoming events').first().json.message.from.first_name }}! This command is not supported yet. Please send text or voice messages.", "additionalFields": {"appendAttribution": false, "parse_mode": "<PERSON><PERSON>"}}, "id": "b5369d4b-d2a8-40e4-bd76-2c8bd14d31b4", "name": "Send error message", "type": "n8n-nodes-base.telegram", "position": [-400, -280], "typeVersion": 1.2, "webhookId": "afeae318-913f-4c1a-a2e2-bf76b695ac9a"}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {"language": "", "temperature": 0.7}}, "id": "97f5f3b8-9611-46cc-ac03-5acd5bb6c886", "name": "Convert audio to text", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-180, 20], "typeVersion": 1.5}, {"parameters": {"content": "## Receive and pre-process messages \n", "height": 547.5630890194532, "width": 1035.4478381373049}, "id": "6e007378-5f9f-4ced-982c-c521be6aca9c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-880, -360], "typeVersion": 1}, {"parameters": {"content": "## 1. Send incoming message to the AI Agent\n## 2. Deliver agent reply to the user \n", "height": 550.5748478134515, "width": 861.262180151035, "color": 2}, "id": "0dec2493-136d-47da-8137-f7662e41f913", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [180, -160], "typeVersion": 1}, {"parameters": {"content": "## Transcribe audio", "height": 194.83713159725437, "width": 367.73614918993235, "color": 6}, "id": "0ce3750d-2671-45b0-9ebf-10b4bafaa6b9", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-420, 0], "typeVersion": 1}, {"parameters": {"operation": "sendChatAction", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}"}, "id": "7e6f26b9-60cb-4391-bcf1-ef907973d3dd", "name": "Send Typing action", "type": "n8n-nodes-base.telegram", "position": [-620, -320], "typeVersion": 1.2, "webhookId": "340d23e5-0bf7-4592-8607-ad23f5b0d93e"}, {"parameters": {"text": "={{ $json.CombinedMessage }}", "options": {"humanMessage": "TOOLS\n------\nAssistant can ask the user to use tools to look up information that may be helpful in answering the users original question. The tools the human can use are:\n\n{tools}\n\n{format_instructions}\n\nUSER'S INPUT\n--------------------\nHere is the user's input (remember to respond with a markdown code snippet of a json blob with a single action, and NOTHING else):\n\n{{input}}", "systemMessage": "=You are a helpful AI assistant. You are chatting with the user named `{{ $('Determine content type').item.json.message.from.first_name }}`. You need to address the user by their name. Today is {{ DateTime.fromISO($now).toLocaleString(DateTime.DATETIME_FULL) }}\n\nIn your reply, always send a message in Telegram-supported HTML format. Here are the formatting instructions:\n1. The following tags are currently supported:\n<b>bold</b>, <strong>bold</strong>\n<i>italic</i>, <em>italic</em>\n<u>underline</u>, <ins>underline</ins>\n<s>strikethrough</s>, <strike>strikethrough</strike>, <del>strikethrough</del>\n<span class=\"tg-spoiler\">spoiler</span>, <tg-spoiler>spoiler</tg-spoiler>\n<b>bold <i>italic bold <s>italic bold strikethrough <span class=\"tg-spoiler\">italic bold strikethrough spoiler</span></s> <u>underline italic bold</u></i> bold</b>\n<a href=\"http://www.example.com/\">inline URL</a>\n<code>inline fixed-width code</code>\n<pre>pre-formatted fixed-width code block</pre>\n2. Any code that you send should be wrapped in these tags: <pre><code class=\"language-python\">pre-formatted fixed-width code block written in the Python programming language</code></pre>\nOther programming languages are supported as well.\n3. All <, > and & symbols that are not a part of a tag or an HTML entity must be replaced with the corresponding HTML entities (< with &lt;, > with &gt; and & with &amp;)\n4. If the user sends you a message starting with / sign, it means this is a Telegram bot command. For example, all users send /start command as their first message. Try to figure out what these commands mean and reply accodringly\n"}}, "id": "ffb2ecff-219f-44ca-af98-c6593898423d", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [260, 20], "typeVersion": 1.1}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.text }}", "rightValue": "/"}]}, "renameOutput": true, "outputKey": "Text"}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "dd41bbf0-bee0-450b-9160-b769821a4abc", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.voice}}", "rightValue": ""}]}, "renameOutput": true, "outputKey": "Voice"}]}, "options": {"fallbackOutput": "extra"}}, "id": "5c86ffbe-0bee-4dad-89e9-4fe443fe8d13", "name": "Determine content type", "type": "n8n-nodes-base.switch", "position": [-620, -120], "typeVersion": 3.2}], "connections": {"AI Agent": {"main": [[{"node": "Send final reply", "type": "main", "index": 0}]]}, "Send final reply": {"main": [[], [{"node": "Correct errors", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Download voice file": {"main": [[{"node": "Convert audio to text", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Convert audio to text": {"main": [[{"node": "Combine content and set properties", "type": "main", "index": 0}]]}, "Determine content type": {"main": [[{"node": "Combine content and set properties", "type": "main", "index": 0}], [{"node": "Download voice file", "type": "main", "index": 0}], [{"node": "Send error message", "type": "main", "index": 0}]]}, "Listen for incoming events": {"main": [[{"node": "Determine content type", "type": "main", "index": 0}, {"node": "Send Typing action", "type": "main", "index": 0}]]}, "Combine content and set properties": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "74b76b67-c8df-4f1f-93d7-fb010d9e5ff3", "triggerCount": 0, "shared": [{"createdAt": "2025-06-30T16:58:04.512Z", "updatedAt": "2025-06-30T16:58:04.512Z", "role": "workflow:owner", "workflowId": "M23PjOZS4h2a3X48", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}