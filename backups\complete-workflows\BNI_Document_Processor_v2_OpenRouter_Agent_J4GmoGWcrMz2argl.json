{"createdAt": "2025-06-08T12:22:41.843Z", "updatedAt": "2025-06-08T22:14:08.000Z", "id": "J4GmoGWcrMz2argl", "name": "BNI Document Processor v2 - OpenRouter Agent", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "bni-document-processor-v2", "responseMode": "responseNode", "options": {"allowedOrigins": "https://bni-presentation-generator.netlify.app,http://localhost:5173,http://localhost:5174,http://localhost:3000,http://localhost:4173"}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 240], "id": "webhook-node", "name": "Document Webhook", "webhookId": "doc-processor-v2"}, {"parameters": {"jsCode": "  // Validate and log incoming data\n  const payload = $input.item.json.body || $input.item.json;\n\n  console.log('=== DOCUMENT PROCESSING REQUEST ===');\n  console.log('Timestamp:', new Date().toISOString());\n  console.log('Payload:', JSON.stringify(payload, null, 2));\n\n  // Check if this is a presentation request with dance_card_url\n  let document_url;\n  let presentation_id;\n\n  if (payload.dance_card_url) {\n    // This is a full presentation request\n    document_url = payload.dance_card_url;\n    presentation_id = payload.presentation_id;\n    console.log('Processing presentation dance card:', document_url);\n  } else if (payload.document_url) {\n    // This is a direct document request\n    document_url = payload.document_url;\n    presentation_id = payload.presentation_id || null;\n  } else {\n    throw new Error('Missing required field: document_url or dance_card_url');\n  }\n\n  // Extract file info\n  const url = document_url;\n  const fileExtension = url.split('.').pop().toLowerCase().split('?')[0]; // Handle URLs with query params\n  const isPDF = fileExtension === 'pdf';\n  const isImage = ['jpg', 'jpeg', 'png'].includes(fileExtension);\n\n  if (!isPDF && !isImage) {\n    throw new Error(`Unsupported file type: ${fileExtension}`);\n  }\n\n  return {\n    json: {\n      document_url: document_url,\n      presentation_id: presentation_id,\n      file_type: isPDF ? 'pdf' : 'image',\n      file_extension: fileExtension,\n      timestamp: new Date().toISOString(),\n      original_payload: payload,\n      // Store presentation data if available\n      presentation_data: payload.dance_card_url ? {\n        business_name: payload.business_name,\n        industry: payload.industry,\n        products_services: payload.products_services,\n        target_audience: payload.target_audience,\n        presentation_goals: payload.presentation_goals,\n        initial_ideas: payload.initial_ideas\n      } : null\n    }\n  };"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 240], "id": "validate-node", "name": "Validate & Prepare Data"}], "connections": {"Document Webhook": {"main": [[{"node": "Validate & Prepare Data", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {"Document Webhook": [{"json": {"headers": {"host": "n8n.brandwisdom.app", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "379", "accept": "*/*", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "en-US,en;q=0.9", "cache-control": "no-cache", "content-type": "application/json", "dnt": "1", "origin": "https://bni-presentation-generator.netlify.app", "pragma": "no-cache", "priority": "u=1, i", "referer": "https://bni-presentation-generator.netlify.app/", "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\"", "sec-fetch-dest": "empty", "sec-fetch-mode": "cors", "sec-fetch-site": "cross-site", "x-forwarded-for": "***************", "x-forwarded-host": "n8n.brandwisdom.app", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "cbac464f6bc1", "x-real-ip": "***************"}, "params": {}, "query": {}, "body": {"presentation_id": "feff250b-32a0-4333-a761-0ed489af91f2", "business_name": "My Business", "industry": "gdhbg", "products_services": "dba", "target_audience": "db", "presentation_goals": "zdthh", "initial_ideas": "", "dance_card_url": "https://unbggjagsowddiemomxv.supabase.co/storage/v1/object/public/dance-cards/4852eb6b-f8f3-479a-b1de-aed8e4276d7f/feff250b-32a0-4333-a761-0ed489af91f2.pdf"}, "webhookUrl": "https://n8n.brandwisdom.app/webhook-test/bni-document-processor-v2", "executionMode": "test"}}]}, "versionId": "a226203b-4ff1-43e9-af0c-95549f414ae2", "triggerCount": 1, "shared": [{"createdAt": "2025-06-08T12:22:41.844Z", "updatedAt": "2025-06-08T12:22:41.844Z", "role": "workflow:owner", "workflowId": "J4GmoGWcrMz2argl", "projectId": "jncwk4CjUI6FYXvX"}], "tags": []}