{"createdAt": "2025-05-30T22:08:22.761Z", "updatedAt": "2025-05-30T22:52:24.392Z", "id": "bstuMX4ETPaZ22uZ", "name": "Basic Working Image Genratin Workflow For SAAS", "active": false, "isArchived": false, "workflow_summary": {"description": "Complete LinkedIn content generation workflow with AI-powered post creation and image generation", "author": "<PERSON>", "key_features": ["Webhook trigger for external integration", "Tavily web search for real-time information", "AI-powered LinkedIn post generation", "AI-generated image prompts", "OpenAI image generation", "Gmail integration for content delivery", "Complete automation pipeline"], "main_components": ["Webhook endpoint", "LinkedIn Post Agent with web search", "Image Prompt Agent", "OpenAI image generation", "Gmail delivery system", "Comprehensive setup documentation"], "integrations": ["OpenRouter API (GPT-4.1)", "Tavily search API", "OpenAI image generation API", "Gmail OAuth2", "Webhook endpoints"], "workflow_steps": ["Receive topic and target audience via webhook", "Search for current information using <PERSON><PERSON>", "Generate professional LinkedIn post", "Create visual prompt for the post", "Generate marketing image using OpenAI", "Convert image to binary format", "Send complete package via Gmail", "Respond to webhook with confirmation"], "setup_requirements": ["OpenRouter API credentials", "Tavily search API key", "OpenAI API key", "Gmail OAuth2 setup"], "note": "This is a complete, production-ready workflow for automated LinkedIn content creation with visual assets."}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "7045acd8-3e34-465a-9f11-d241609718b0", "triggerCount": 1, "shared": [{"createdAt": "2025-05-30T22:08:22.765Z", "updatedAt": "2025-05-30T22:08:22.765Z", "role": "workflow:owner", "workflowId": "bstuMX4ETPaZ22uZ", "projectId": "jncwk4CjUI6FYXvX"}], "tags": []}