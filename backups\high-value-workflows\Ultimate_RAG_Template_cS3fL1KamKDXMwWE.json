{"createdAt": "2025-06-08T08:55:36.990Z", "updatedAt": "2025-06-08T08:56:30.000Z", "id": "cS3fL1KamKDXMwWE", "name": "## 🚀 Ultimate n8n Agentic RAG Template", "active": false, "isArchived": false, "nodes": [{"parameters": {"options": {}}, "id": "85f0bf99-ce7c-42f8-a5eb-7bdf31e4c381", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [-740, 360]}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "=file_id", "value": "={{ $('Set File ID').first().json.file_id }}"}, {"name": "file_title", "value": "={{ $('Set File ID').first().json.file_title }}"}]}}}, "id": "cafbaa27-e22c-4324-a116-6ea2f7e87a17", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1000, 1140]}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "3d2f156a-7638-488f-821a-745b6cf8f72f", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [760, 1140]}, {"parameters": {"content": "## Agent Tools for RAG", "height": 528.85546469693, "width": 583.4552380860637, "color": 4}, "id": "32add93c-4693-4bac-a451-fea358089439", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0]}, {"parameters": {"content": "## Tool to Add a Google Drive File to Vector DB", "height": 867, "width": 3073, "color": 5}, "id": "eef29988-543a-4012-9873-013f9260c9b9", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1740, 540]}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Set File ID').item.json.file_id }}", "mode": "id"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}}, "id": "bc148934-f3ae-4154-83fc-8553022db7a3", "name": "Download File", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-660, 820], "executeOnce": true}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9", "mode": "list", "cachedResultName": "n8n Documents", "cachedResultUrl": "https://drive.google.com/drive/folders/1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9"}, "event": "fileCreated", "options": {}}, "id": "af5ebce7-78d2-4155-9a28-8208969c884d", "name": "File Created", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1680, 660]}], "workflow_summary": {"description": "Ultimate n8n Agentic RAG Template - A comprehensive RAG system with intelligent tool selection", "author": "<PERSON>", "key_features": ["Intelligent tool selection between RAG lookups, SQL queries, and full document retrieval", "Complete document context access instead of just chunks", "Accurate numerical analysis using SQL for tabular data", "Cross-document insights across entire knowledge base", "Multi-file processing in single workflow loop", "Efficient JSONB storage in Supabase for tabular data"], "node_count": 45, "main_components": ["OpenAI Chat Model for LLM", "Google Drive integration for file storage", "Supabase vector store for embeddings", "PostgreSQL for structured data storage", "Multiple file format processors (PDF, Excel, CSV, Docs)", "RAG AI Agent with multiple tools", "Chat interface with webhook triggers"], "database_tables": ["documents - Vector embeddings storage", "document_metadata - File metadata and schemas", "document_rows - Tabular data storage"], "tools_available": ["RAG lookup in documents table", "List available documents", "Get full file contents", "SQL queries on tabular data"], "file_formats_supported": ["PDF", "Google Docs", "Google Sheets", "Excel (XLSX)", "CSV", "Plain text"], "note": "This workflow contains extensive pinned data and complex node configurations. Full workflow data exceeds reasonable file size limits."}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": null, "versionId": "1c133eca-2511-4565-98db-9fa4de709165", "triggerCount": 0, "shared": [{"createdAt": "2025-06-08T08:55:36.994Z", "updatedAt": "2025-06-08T08:55:36.994Z", "role": "workflow:owner", "workflowId": "cS3fL1KamKDXMwWE", "projectId": "jncwk4CjUI6FYXvX"}], "tags": []}