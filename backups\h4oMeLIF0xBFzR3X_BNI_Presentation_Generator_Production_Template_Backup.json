{"createdAt": "2025-06-07T21:54:35.760Z", "updatedAt": "2025-06-07T21:54:35.760Z", "id": "h4oMeLIF0xBFzR3X", "name": "BNI Presentation Generator - Production Template Backup", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "bni-presentation-generator", "options": {}}, "id": "a0cfb068-27b0-411a-b15a-aa258fc3810c", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300]}, {"parameters": {"jsCode": "// Validate and structure incoming webhook data\nconst payload = $input.item.json;\n\n// Log incoming data for debugging\nconsole.log('Received webhook payload:', JSON.stringify(payload, null, 2));\n\n// Validate required fields\nconst requiredFields = [\n  'presentation_id',\n  'business_name', \n  'industry',\n  'products_services',\n  'target_audience',\n  'presentation_goals'\n];\n\nconst missingFields = requiredFields.filter(field => !payload[field]);\n\nif (missingFields.length > 0) {\n  throw new Error(`Missing required fields: ${missingFields.join(', ')}`);\n}\n\n// Structure the data for processing\nreturn {\n  json: {\n    presentation_id: payload.presentation_id,\n    business_info: {\n      name: payload.business_name,\n      industry: payload.industry,\n      products_services: payload.products_services,\n      target_audience: payload.target_audience\n    },\n    presentation_goals: payload.presentation_goals,\n    initial_ideas: payload.initial_ideas || '',\n    dance_card_url: payload.dance_card_url || null,\n    timestamp: new Date().toISOString()\n  }\n};"}, "id": "1d0e2bae-2853-4566-a62b-5398f3cdce57", "name": "Data Validation & Structuring", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "dance-card-check", "leftValue": "={{ $json.dance_card_url }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combinator": "and"}, "options": {}}, "id": "7d0ce9bf-c7e9-498a-a6bb-35ffe62f9e07", "name": "Check Dance Card", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"url": "={{ $json.dance_card_url }}", "authentication": "none", "requestMethod": "GET", "options": {}}, "id": "36fcdbb1-ea6a-46db-8fad-571d4df46593", "name": "Download Dance Card", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [680, 120]}, {"parameters": {"jsCode": "// Process dance card content\nconst inputData = $input.item.json;\nlet danceCardInfo = '';\n\n// Check if we have dance card data\nif (inputData.data) {\n  // For MVP, we'll extract basic info from the file\n  // In production, this would use OCR or PDF parsing\n  danceCardInfo = 'Dance card content processed. File contains member networking information and referral guidelines.';\n  \n  console.log('Dance card processed successfully');\n} else {\n  danceCardInfo = 'No dance card content available.';\n}\n\n// Merge with existing data\nreturn {\n  json: {\n    ...inputData,\n    dance_card_info: danceCardInfo,\n    dance_card_processed: true\n  }\n};"}, "id": "35aaa07d-b0bf-487d-9118-************", "name": "Process Dance Card Content", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 120]}, {"parameters": {"jsCode": "// Merge data streams (with or without dance card)\nconst inputData = $input.item.json;\n\n// Ensure we have dance card info field\nif (!inputData.dance_card_info) {\n  inputData.dance_card_info = 'No dance card provided.';\n  inputData.dance_card_processed = false;\n}\n\nreturn {\n  json: inputData\n};"}, "id": "15ce2aa5-b377-460a-8394-dc5533242d4d", "name": "Merge Data Streams", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"jsCode": "// Prepare AI prompt for first presentation concept\nconst data = $input.item.json;\n\n// Create comprehensive prompt for BNI presentation\nconst prompt = `You are an expert BNI Feature Presentation consultant with deep knowledge of BNI networking principles and effective presentation strategies.\n\nYour task is to create a compelling 8-10 minute BNI Feature Presentation concept that will help the member generate quality referrals and educate their chapter about their business.\n\nBUSINESS INFORMATION:\n- Business Name: ${data.business_info.name}\n- Industry: ${data.business_info.industry}\n- Products/Services: ${data.business_info.products_services}\n- Target Audience: ${data.business_info.target_audience}\n\nPRESENTATION GOALS:\n${data.presentation_goals}\n\n${data.initial_ideas ? `INITIAL IDEAS PROVIDED:\\n${data.initial_ideas}\\n\\n` : ''}\n${data.dance_card_info ? `DANCE CARD CONTEXT:\\n${data.dance_card_info}\\n\\n` : ''}\n\nBNI FEATURE PRESENTATION GUIDELINES:\n- Must be 8-10 minutes long\n- Should educate the chapter about your business\n- Must include specific referral asks\n- Should tell stories that illustrate your expertise\n- Must be engaging and memorable\n- Should position you as the expert in your field\n\nCREATE A STRUCTURED PRESENTATION CONCEPT WITH:\n\n1. **Compelling Title**: Create an attention-grabbing title that reflects the business value\n2. **Opening Hook**: Design a powerful 30-second opening that immediately captures attention\n3. **Time-Structured Content**: Break down the 8-10 minutes with specific time allocations\n4. **Story Elements**: Include 2-3 specific story suggestions that demonstrate expertise\n5. **Educational Content**: Information that teaches the chapter about the industry/service\n6. **Referral Requests**: 3-4 specific, actionable referral asks\n7. **Prop Recommendations**: Physical items or visual aids to enhance engagement\n8. **Call to Action**: Clear next steps for chapter members\n\nFormat your response as a valid JSON object with this exact structure:\n\n{\n  \"title\": \"Engaging presentation title\",\n  \"hook\": \"Detailed description of the opening hook strategy\",\n  \"structure\": [\n    {\n      \"section\": \"Section name\",\n      \"timeAllocation\": \"X minutes\",\n      \"content\": \"Detailed description of what to cover in this section\"\n    }\n  ],\n  \"storyIdeas\": [\n    \"Specific story idea with context and purpose\",\n    \"Another story idea that demonstrates expertise\",\n    \"Third story that connects to referral opportunities\"\n  ],\n  \"referralAsks\": [\n    \"Specific type of referral with clear criteria\",\n    \"Another referral ask with actionable details\",\n    \"Third referral request with qualification guidelines\"\n  ],\n  \"propRecommendations\": [\n    \"Physical prop with usage explanation\",\n    \"Visual aid with implementation details\",\n    \"Interactive element with engagement strategy\"\n  ],\n  \"callToAction\": \"Clear next steps for chapter members to take\",\n  \"keyMessages\": [\n    \"Primary message about business value\",\n    \"Secondary message about expertise\",\n    \"Tertiary message about referral process\"\n  ]\n}\n\nEnsure the presentation concept is professional, engaging, and specifically tailored to BNI networking principles.`;\n\nreturn {\n  json: {\n    ...data,\n    ai_prompt_concept1: prompt\n  }\n};"}, "id": "c50d0bee-d84d-4a1f-9c84-1a82957ec6cf", "name": "Prepare AI Prompt - Concept 1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"resource": "chat", "operation": "message", "model": "gpt-4", "messages": {"values": [{"role": "system", "content": "You are an expert BNI Feature Presentation consultant who creates structured, engaging presentation concepts in valid JSON format. You understand BNI networking principles and create presentations that generate quality referrals."}, {"role": "user", "content": "={{ $json.ai_prompt_concept1 }}"}]}, "options": {"temperature": 0.7, "maxTokens": 2500}}, "id": "965385d6-2626-468f-8db5-75c31ce45bd7", "name": "Generate Concept 1 - OpenAI", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"jsCode": "// Parse and validate OpenAI response for concept 1\nconst response = $input.item.json;\nlet concept1;\n\ntry {\n  // Extract content from OpenAI response\n  const content = response.choices[0].message.content.trim();\n  \n  console.log('OpenAI Concept 1 Response:', content);\n  \n  // Find and extract JSON content\n  const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n  \n  if (jsonMatch) {\n    concept1 = JSON.parse(jsonMatch[0]);\n    \n    // Validate required fields\n    const requiredFields = ['title', 'hook', 'structure', 'storyIdeas', 'referralAsks', 'propRecommendations'];\n    const missingFields = requiredFields.filter(field => !concept1[field]);\n    \n    if (missingFields.length > 0) {\n      throw new Error(`Concept 1 missing required fields: ${missingFields.join(', ')}`);\n    }\n    \n    console.log('Concept 1 parsed successfully');\n  } else {\n    throw new Error('No valid JSON found in OpenAI response');\n  }\n} catch (error) {\n  console.error('Error parsing Concept 1:', error.message);\n  throw new Error(`Failed to parse Concept 1: ${error.message}`);\n}\n\n// Return data with parsed concept\nreturn {\n  json: {\n    ...$input.item.json,\n    concept1: concept1,\n    concept1_generated_at: new Date().toISOString()\n  }\n};"}, "id": "0119624b-4406-4443-896e-3b29ca402ccd", "name": "Parse & Validate Concept 1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 300]}, {"parameters": {"jsCode": "// Prepare AI prompt for second presentation concept\nconst data = $input.item.json;\n\n// Create a different approach for concept 2\nconst prompt = `You are an expert BNI Feature Presentation consultant. Create a COMPLETELY DIFFERENT presentation concept from the first one.\n\nPREVIOUS CONCEPT TITLE: \"${data.concept1.title}\"\n\nYour task is to create a second, distinctly different 8-10 minute BNI Feature Presentation concept using a different approach, style, and structure.\n\nBUSINESS INFORMATION:\n- Business Name: ${data.business_info.name}\n- Industry: ${data.business_info.industry}\n- Products/Services: ${data.business_info.products_services}\n- Target Audience: ${data.business_info.target_audience}\n\nPRESENTATION GOALS:\n${data.presentation_goals}\n\n${data.initial_ideas ? `INITIAL IDEAS PROVIDED:\\n${data.initial_ideas}\\n\\n` : ''}\n${data.dance_card_info ? `DANCE CARD CONTEXT:\\n${data.dance_card_info}\\n\\n` : ''}\n\nDIFFERENTIATION REQUIREMENTS:\n- Use a completely different presentation style than the first concept\n- Choose a different angle or perspective on the business\n- Use different types of stories and examples\n- Employ different engagement techniques\n- Focus on different aspects of the business value proposition\n\nBNI FEATURE PRESENTATION GUIDELINES:\n- Must be 8-10 minutes long\n- Should educate the chapter about your business\n- Must include specific referral asks\n- Should tell stories that illustrate your expertise\n- Must be engaging and memorable\n- Should position you as the expert in your field\n\nCREATE A SECOND STRUCTURED PRESENTATION CONCEPT WITH:\n\n1. **Different Title**: Create a title with a different tone/approach than \"${data.concept1.title}\"\n2. **Alternative Hook**: Use a different opening strategy (if first was story-based, try question-based, etc.)\n3. **Varied Structure**: Different time allocation and flow than the first concept\n4. **New Stories**: Completely different story suggestions that show different aspects of expertise\n5. **Alternative Education**: Different educational angle about the industry/service\n6. **Different Referrals**: Alternative referral asks that complement but don't duplicate the first concept\n7. **Varied Props**: Different types of props and visual aids\n8. **Alternative CTA**: Different call to action approach\n\nFormat your response as a valid JSON object with this exact structure:\n\n{\n  \"title\": \"Different engaging presentation title\",\n  \"hook\": \"Alternative opening hook strategy\",\n  \"structure\": [\n    {\n      \"section\": \"Section name\",\n      \"timeAllocation\": \"X minutes\",\n      \"content\": \"Detailed description of what to cover\"\n    }\n  ],\n  \"storyIdeas\": [\n    \"Different story idea with unique context\",\n    \"Alternative story demonstrating different expertise\",\n    \"Third story with different referral connection\"\n  ],\n  \"referralAsks\": [\n    \"Different type of referral with clear criteria\",\n    \"Alternative referral ask with unique details\",\n    \"Third different referral request\"\n  ],\n  \"propRecommendations\": [\n    \"Different physical prop with usage explanation\",\n    \"Alternative visual aid with implementation\",\n    \"Different interactive element\"\n  ],\n  \"callToAction\": \"Alternative next steps for chapter members\",\n  \"keyMessages\": [\n    \"Different primary message about business value\",\n    \"Alternative message about expertise\",\n    \"Different message about referral process\"\n  ]\n}\n\nEnsure this second concept offers a genuinely different approach while maintaining BNI networking effectiveness.`;\n\nreturn {\n  json: {\n    ...data,\n    ai_prompt_concept2: prompt\n  }\n};"}, "id": "4b23d850-353d-40bb-bdfc-d8846f04fc86", "name": "Prepare AI Prompt - Concept 2", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 300]}, {"parameters": {"resource": "chat", "operation": "message", "model": "gpt-4", "messages": {"values": [{"role": "system", "content": "You are an expert BNI Feature Presentation consultant who creates diverse, structured presentation concepts in valid JSON format. You excel at creating multiple different approaches for the same business while maintaining BNI networking effectiveness."}, {"role": "user", "content": "={{ $json.ai_prompt_concept2 }}"}]}, "options": {"temperature": 0.8, "maxTokens": 2500}}, "id": "223b70f1-5883-4bbb-9d48-80cc24936114", "name": "Generate Concept 2 - OpenAI", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [2000, 300]}, {"parameters": {"jsCode": "// Parse and validate OpenAI response for concept 2\nconst response = $input.item.json;\nlet concept2;\n\ntry {\n  // Extract content from OpenAI response\n  const content = response.choices[0].message.content.trim();\n  \n  console.log('OpenAI Concept 2 Response:', content);\n  \n  // Find and extract JSON content\n  const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n  \n  if (jsonMatch) {\n    concept2 = JSON.parse(jsonMatch[0]);\n    \n    // Validate required fields\n    const requiredFields = ['title', 'hook', 'structure', 'storyIdeas', 'referralAsks', 'propRecommendations'];\n    const missingFields = requiredFields.filter(field => !concept2[field]);\n    \n    if (missingFields.length > 0) {\n      throw new Error(`Concept 2 missing required fields: ${missingFields.join(', ')}`);\n    }\n    \n    console.log('Concept 2 parsed successfully');\n  } else {\n    throw new Error('No valid JSON found in OpenAI response');\n  }\n} catch (error) {\n  console.error('Error parsing Concept 2:', error.message);\n  throw new Error(`Failed to parse Concept 2: ${error.message}`);\n}\n\n// Return data with both concepts\nreturn {\n  json: {\n    ...$input.item.json,\n    concept2: concept2,\n    concept2_generated_at: new Date().toISOString()\n  }\n};"}, "id": "9e4cab3b-e131-4aa9-9734-cafc47afedc7", "name": "Parse & Validate Concept 2", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2220, 300]}, {"parameters": {"jsCode": "// Format final results for database storage\nconst data = $input.item.json;\n\n// Create structured result object\nconst result = {\n  presentation_id: data.presentation_id,\n  business_name: data.business_info.name,\n  processing_completed_at: new Date().toISOString(),\n  concept_1: {\n    ...data.concept1,\n    generated_at: data.concept1_generated_at\n  },\n  concept_2: {\n    ...data.concept2,\n    generated_at: data.concept2_generated_at\n  },\n  metadata: {\n    dance_card_processed: data.dance_card_processed || false,\n    processing_duration: 'calculated_in_supabase',\n    ai_model: 'gpt-4',\n    workflow_version: '2.0'\n  }\n};\n\nconsole.log('Final result prepared:', JSON.stringify(result, null, 2));\n\nreturn {\n  json: result\n};"}, "id": "29c4d5eb-e765-4cc0-a1b8-23a31009f4ca", "name": "Format Final Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2440, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Insert both presentation concepts into presentation_results table\nINSERT INTO presentation_results (\n  presentation_id,\n  concept_number,\n  title,\n  hook,\n  structure,\n  story_suggestions,\n  referral_asks,\n  prop_recommendations,\n  full_content,\n  created_at\n) VALUES \n(\n  '{{ $json.presentation_id }}',\n  1,\n  '{{ $json.concept_1.title }}',\n  '{{ $json.concept_1.hook }}',\n  '{{ JSON.stringify($json.concept_1.structure) }}',\n  '{{ JSON.stringify($json.concept_1.storyIdeas) }}',\n  '{{ JSON.stringify($json.concept_1.referralAsks) }}',\n  '{{ JSON.stringify($json.concept_1.propRecommendations) }}',\n  '{{ JSON.stringify($json.concept_1) }}',\n  NOW()\n),\n(\n  '{{ $json.presentation_id }}',\n  2,\n  '{{ $json.concept_2.title }}',\n  '{{ $json.concept_2.hook }}',\n  '{{ JSON.stringify($json.concept_2.structure) }}',\n  '{{ JSON.stringify($json.concept_2.storyIdeas) }}',\n  '{{ JSON.stringify($json.concept_2.referralAsks) }}',\n  '{{ JSON.stringify($json.concept_2.propRecommendations) }}',\n  '{{ JSON.stringify($json.concept_2) }}',\n  NOW()\n);"}, "id": "34e39cca-d191-4888-8d01-a6a868e0c8fb", "name": "Save Results to Supabase", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2660, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Update presentation status to completed\nUPDATE presentations \nSET \n  status = 'completed',\n  updated_at = NOW()\nWHERE id = '{{ $json.presentation_id }}';"}, "id": "1d824ccb-38a4-4036-84d8-e6450b40671d", "name": "Update Status - Completed", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2880, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  success: true,\n  message: 'BNI presentation concepts generated successfully',\n  presentation_id: $json.presentation_id,\n  concepts_generated: 2,\n  processing_completed_at: $json.processing_completed_at\n} }}", "options": {}}, "id": "eda555da-b7dc-4aac-9e55-fc6b271d24ae", "name": "Respond - Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [3100, 300]}, {"parameters": {}, "id": "7885ccbb-cc2e-4122-b2a9-990477c93337", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [1560, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Update presentation status to error\nUPDATE presentations \nSET \n  status = 'error',\n  updated_at = NOW()\nWHERE id = '{{ $json.presentation_id || \"unknown\" }}';"}, "id": "039f682a-5de6-491f-9926-cba25668c02f", "name": "Update Status - Error", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1780, 500]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  success: false,\n  message: 'Error generating BNI presentation concepts',\n  error: $error.message || 'Unknown error occurred',\n  presentation_id: $json.presentation_id || 'unknown',\n  timestamp: new Date().toISOString()\n} }}", "options": {}}, "id": "38aaa146-edfc-4826-882d-a3b76d824c8b", "name": "Respond - Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2000, 500]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Data Validation & Structuring", "type": "main", "index": 0}]]}, "Data Validation & Structuring": {"main": [[{"node": "Check Dance Card", "type": "main", "index": 0}]]}, "Check Dance Card": {"main": [[{"node": "Download Dance Card", "type": "main", "index": 0}], [{"node": "Merge Data Streams", "type": "main", "index": 0}]]}, "Download Dance Card": {"main": [[{"node": "Process Dance Card Content", "type": "main", "index": 0}]]}, "Process Dance Card Content": {"main": [[{"node": "Merge Data Streams", "type": "main", "index": 0}]]}, "Merge Data Streams": {"main": [[{"node": "Prepare AI Prompt - Concept 1", "type": "main", "index": 0}]]}, "Prepare AI Prompt - Concept 1": {"main": [[{"node": "Generate Concept 1 - OpenAI", "type": "main", "index": 0}]]}, "Generate Concept 1 - OpenAI": {"main": [[{"node": "Parse & Validate Concept 1", "type": "main", "index": 0}]]}, "Parse & Validate Concept 1": {"main": [[{"node": "Prepare AI Prompt - Concept 2", "type": "main", "index": 0}]]}, "Prepare AI Prompt - Concept 2": {"main": [[{"node": "Generate Concept 2 - OpenAI", "type": "main", "index": 0}]]}, "Generate Concept 2 - OpenAI": {"main": [[{"node": "Parse & Validate Concept 2", "type": "main", "index": 0}]]}, "Parse & Validate Concept 2": {"main": [[{"node": "Format Final Results", "type": "main", "index": 0}]]}, "Format Final Results": {"main": [[{"node": "Save Results to Supabase", "type": "main", "index": 0}]]}, "Save Results to Supabase": {"main": [[{"node": "Update Status - Completed", "type": "main", "index": 0}]]}, "Update Status - Completed": {"main": [[{"node": "Respond - Success", "type": "main", "index": 0}]]}, "Error Trigger": {"main": [[{"node": "Update Status - Error", "type": "main", "index": 0}]]}, "Update Status - Error": {"main": [[{"node": "Respond - Error", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": null, "pinData": null, "versionId": "bbec024b-b934-40ee-b789-90d3e6a81eb7", "triggerCount": 0, "shared": [{"createdAt": "2025-06-07T21:54:35.765Z", "updatedAt": "2025-06-07T21:54:35.765Z", "role": "workflow:owner", "workflowId": "h4oMeLIF0xBFzR3X", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}