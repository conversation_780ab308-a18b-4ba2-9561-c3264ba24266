{"createdAt": "2025-06-07T22:29:35.533Z", "updatedAt": "2025-06-07T22:43:40.000Z", "id": "JKShE98n1SuvXZt7", "name": "BNI Presentation Generator - Clean Architecture", "active": false, "isArchived": false, "workflow_summary": {"description": "Complete BNI presentation generator with clean architecture, AI-powered concept generation, and comprehensive error handling", "key_features": ["Webhook trigger for external integration", "Data validation and preparation", "AI-powered presentation concept generation", "Dual concept creation (Professional/Educational vs Story-driven/Personal)", "OpenRouter integration with DeepSeek model", "Conversation memory for context", "Supabase database integration", "Comprehensive error handling", "JSON response formatting"], "workflow_structure": ["Webhook receives presentation request", "Data validation ensures required fields", "AI agent generates two distinct presentation concepts", "Response parsing and validation", "Database storage of results", "Status updates and webhook response"], "integrations": ["OpenRouter API (DeepSeek model)", "Supabase database", "Webhook endpoints"], "ai_capabilities": ["BNI-specific presentation expertise", "Dual concept generation with different approaches", "Structured JSON output", "Professional presentation frameworks", "Story-driven content creation"], "note": "This is a production-ready workflow with clean architecture and comprehensive BNI presentation generation capabilities."}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "9137acde-310e-456a-88bc-f34bfd538434", "triggerCount": 0, "shared": [{"createdAt": "2025-06-07T22:29:35.535Z", "updatedAt": "2025-06-07T22:29:35.535Z", "role": "workflow:owner", "workflowId": "JKShE98n1SuvXZt7", "projectId": "jncwk4CjUI6FYXvX"}], "tags": []}