{"createdAt": "2025-05-30T22:52:15.278Z", "updatedAt": "2025-05-31T14:17:38.000Z", "id": "31t9nWTe22naF3Nh", "name": "LinkedIn SAAS - Fixed Webhook POST", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "8da077f9-612d-438e-8813-72694186d247", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [840, 120], "id": "webhook-trigger", "name": "Webhook", "webhookId": "a87b0436-11bb-42d1-9315-e9f45cdd807a"}, {"parameters": {"toolDescription": "Use this tool to search the web.", "method": "POST", "url": "https://api.tavily.com/search", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"query\": \"{searchTerm}\",\n  \"topic\": \"general\",\n  \"search_depth\": \"advanced\",\n  \"chunks_per_source\": 3,\n  \"max_results\": 1,\n  \"time_range\": null,\n  \"days\": 7,\n  \"include_answer\": true,\n  \"include_raw_content\": false,\n  \"include_images\": false,\n  \"include_image_descriptions\": false,\n  \"include_domains\": [],\n  \"exclude_domains\": []\n}", "placeholderDefinitions": {"values": [{"name": "searchTerm", "description": "What the user is searching for."}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1120, 380], "id": "tavily-tool", "name": "<PERSON><PERSON>"}], "connections": {"Webhook": {"main": [[{"node": "LinkedIn Post Agent", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": null, "versionId": "08dd2db6-9828-49c3-a6df-57c7c5bf0ed1", "triggerCount": 1, "backup_metadata": {"backup_status": "EXACT_REPLICA_FROM_N8N", "backup_timestamp": "2025-01-03T00:00:00Z", "source": "n8n_mcp_get_workflow", "workflow_complexity": "very_high", "node_count": 10, "workflow_type": "linkedin_content_generation_saas"}}