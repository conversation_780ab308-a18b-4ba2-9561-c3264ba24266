{"createdAt": "2025-06-09T09:08:29.468Z", "updatedAt": "2025-06-13T12:23:22.000Z", "id": "XJDEslazu5PowwxK", "name": "BNI Dance Card OCR - Comprehensive Fields", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "bni-dance-card-ocr-comprehensive", "responseMode": "responseNode", "options": {"allowedOrigins": "https://bni-presentation-generator.netlify.app,http://localhost:5173,http://localhost:5174,http://localhost:3000,http://localhost:4173"}}, "name": "Dance Card Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [220, 340], "id": "webhook-node", "webhookId": "bni-dance-card-ocr-comprehensive"}, {"parameters": {"jsCode": "// Comprehensive validation and data preparation for BNI Dance Card OCR\nconst payload = $input.item.json.body || $input.item.json;\n\nconsole.log('=== BNI DANCE CARD OCR - COMPREHENSIVE FIELDS ===');\nconsole.log('Timestamp:', new Date().toISOString());\nconsole.log('Payload:', JSON.stringify(payload, null, 2));\n\n// Initialize variables\nlet document_url;\nlet uploadId;\nlet filename = 'dance-card'; // Default filename\n\n// Check for document URL in multiple possible locations\nif (payload.dance_card_url) {\n  document_url = payload.dance_card_url;\n  uploadId = payload.uploadId;\n  console.log('Processing presentation dance card:', document_url);\n} else if (payload.document_url) {\n  document_url = payload.document_url;\n  uploadId = payload.uploadId || null;\n} else {\n  throw new Error('Missing required field: document_url or dance_card_url');\n}\n\n// Extract filename with robust URL parsing\ntry {\n  const urlObj = new URL(document_url);\n  const pathParts = urlObj.pathname.split('/');\n  const lastSegment = pathParts[pathParts.length - 1];\n  \n  if (lastSegment) {\n    // Remove query parameters and decode URI\n    filename = decodeURIComponent(lastSegment.split('?')[0]);\n  }\n} catch (e) {\n  console.warn('Could not parse URL, attempting basic extraction:', e.message);\n  // Fallback: basic string manipulation\n  const urlParts = document_url.split('/');\n  const lastPart = urlParts[urlParts.length - 1];\n  if (lastPart) {\n    filename = lastPart.split('?')[0];\n  }\n}\n\n// Extract and validate file extension\nconst fileExtension = filename.split('.').pop().toLowerCase();\nconst supportedTypes = ['pdf', 'jpg', 'jpeg', 'png', 'docx', 'pptx'];\n\nif (!supportedTypes.includes(fileExtension)) {\n  throw new Error(`Unsupported file type: ${fileExtension}. Supported types: ${supportedTypes.join(', ')}`);\n}\n\n// Determine file type category\nconst fileType = fileExtension === 'pdf' ? 'pdf' : \n                ['jpg', 'jpeg', 'png'].includes(fileExtension) ? 'image' : \n                'document';\n\n// Prepare output data\nreturn {\n  json: {\n    document_url: document_url,\n    upload_id: uploadId,\n    filename: filename,\n    file_extension: fileExtension,\n    file_type: fileType,\n    timestamp: new Date().toISOString(),\n    original_payload: payload,\n    // Metadata for tracking\n    processing_metadata: {\n      workflow: 'bni-dance-card-ocr-comprehensive',\n      version: '3.0',\n      environment: 'production'\n    }\n  }\n};"}, "name": "Validate & Prepare Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, 340], "id": "validate-node"}, {"parameters": {"url": "={{ $json.document_url }}", "options": {"response": {"response": {"responseFormat": "file"}}, "timeout": 30000}}, "name": "Download Document", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [660, 340], "id": "download-node"}, {"parameters": {"method": "POST", "url": "https://api.mistral.ai/v1/files", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "data"}, {"name": "purpose", "value": "ocr"}]}, "options": {}}, "name": "Mistral - Upload File", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [880, 340], "id": "mistral-upload", "credentials": {"httpHeaderAuth": {"id": "gqJibJ5zG59mTjvy", "name": "Mistral OCR"}}}, {"parameters": {"url": "=https://api.mistral.ai/v1/files/{{ $json.id }}/url", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "name": "Mistral - Get Signed URL", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1100, 340], "id": "mistral-signed-url", "credentials": {"httpHeaderAuth": {"id": "gqJibJ5zG59mTjvy", "name": "Mistral OCR"}}}, {"parameters": {"method": "POST", "url": "https://api.mistral.ai/v1/ocr", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"mistral-ocr-latest\",\n  \"document\": {\n    \"type\": \"document_url\",\n    \"document_url\": \"{{ $json.url }}\"\n  },\n  \"include_image_base64\": false\n}", "options": {"response": {"response": {}}}}, "name": "Mistral - OCR Process", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1320, 340], "id": "mistral-ocr", "credentials": {"httpHeaderAuth": {"id": "gqJibJ5zG59mTjvy", "name": "Mistral OCR"}}}, {"parameters": {"jsCode": "// Extract and prepare OCR text for comprehensive LLM processing\nconst ocrResponse = $input.item.json;\nconst validationData = $('Validate & Prepare Data').item.json;\n\nconsole.log('=== PREPARING OCR TEXT FOR COMPREHENSIVE LLM ===');\nconsole.log('OCR Response Keys:', Object.keys(ocrResponse));\n\nlet ocrText = '';\nlet ocrMetadata = {\n  provider: 'mistral',\n  model: 'mistral-ocr-latest',\n  processed_at: new Date().toISOString(),\n  status: 'unknown'\n};\n\ntry {\n  // Extract text from Mistral OCR response\n  const ocrOutput = ocrResponse['ocr_output.json'];\n  \n  if (typeof ocrOutput === 'string') {\n    try {\n      const parsedOutput = JSON.parse(ocrOutput);\n      \n      // Mistral OCR response structure\n      if (parsedOutput.text) {\n        ocrText = parsedOutput.text;\n        ocrMetadata.status = 'success';\n        ocrMetadata.confidence = parsedOutput.confidence || null;\n        ocrMetadata.pages = parsedOutput.pages || 1;\n      } else if (parsedOutput.content) {\n        ocrText = parsedOutput.content;\n        ocrMetadata.status = 'success';\n      } else {\n        // Fallback: use the entire parsed output as text\n        ocrText = JSON.stringify(parsedOutput, null, 2);\n        ocrMetadata.status = 'fallback';\n      }\n    } catch (parseError) {\n      // If JSON parsing fails, use raw string\n      ocrText = ocrOutput;\n      ocrMetadata.status = 'raw_text';\n      ocrMetadata.parse_error = parseError.message;\n    }\n  } else if (typeof ocrOutput === 'object') {\n    // Already parsed object\n    if (ocrOutput.text) {\n      ocrText = ocrOutput.text;\n      ocrMetadata.status = 'success';\n    } else {\n      ocrText = JSON.stringify(ocrOutput, null, 2);\n      ocrMetadata.status = 'object_fallback';\n    }\n  } else {\n    throw new Error('No OCR output found in response');\n  }\n  \n  // Validate we have meaningful text\n  if (!ocrText || ocrText.trim().length < 10) {\n    throw new Error('OCR text too short or empty');\n  }\n  \n  console.log('OCR Text Length:', ocrText.length);\n  console.log('OCR Metadata:', ocrMetadata);\n  console.log('First 500 chars:', ocrText.substring(0, 500));\n  \n} catch (error) {\n  console.error('Error processing OCR text:', error.message);\n  ocrMetadata.status = 'error';\n  ocrMetadata.error = error.message;\n  \n  // Fallback: try to extract any text from the response\n  try {\n    const responseStr = JSON.stringify(ocrResponse, null, 2);\n    ocrText = `OCR processing error: ${error.message}\\n\\nRaw response:\\n${responseStr}`;\n  } catch {\n    ocrText = `OCR processing failed: ${error.message}`;\n  }\n}\n\n// Return prepared data for comprehensive Information Extractor\nreturn {\n  json: {\n    ocr_text: ocrText,\n    ocr_metadata: ocrMetadata,\n    original_ocr_response: ocrResponse,\n    validation_data: validationData,\n    prepared_for_llm: true,\n    timestamp: new Date().toISOString()\n  }\n};"}, "name": "Prepare OCR Text for LLM", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1540, 340], "id": "prepare-ocr-text"}, {"parameters": {"text": "={{ $json.ocr_text }}", "attributes": {"attributes": [{"name": "profession", "description": "BNI Category/Profession (e.g., 'Digital Marketing', 'Real Estate', 'Insurance')", "required": true}, {"name": "business_name", "description": "Name of the business/company"}, {"name": "years_in_business", "description": "How many years in this business"}, {"name": "phone", "description": "Business phone number"}, {"name": "email", "description": "Business email address"}, {"name": "website", "description": "Business website URL"}, {"name": "burning_desire", "description": "My burning desire is to... (member's passion/mission)"}, {"name": "key_to_success", "description": "My key to success (unique approach or methodology)"}, {"name": "unique_fact", "description": "Something no one knows about me (unique or surprising fact)"}, {"name": "previous_experience", "description": "Previous types of jobs/experience/background"}, {"name": "goals", "description": "Goals (business or personal goals, typically 3 items)"}, {"name": "accomplishments", "description": "Accomplishments (key achievements, typically 3 items)"}, {"name": "interests", "description": "Interests (professional or personal interests, typically 3 items)"}, {"name": "networks", "description": "Networks (groups/associations member belongs to, typically 3 items)"}, {"name": "skills", "description": "Skills (what member is good at, typically 5 items)"}, {"name": "revenue_goal", "description": "Revenue Goal (annual or monthly revenue target)"}, {"name": "average_deal", "description": "Average deal size or transaction value"}, {"name": "deals_per_month", "description": "Number of deals/transactions per month"}, {"name": "things_to_listen_for", "description": "Things to LISTEN for (keywords/phrases that indicate referral opportunities)"}, {"name": "things_to_look_for", "description": "Things to LOOK OUT for (situations that need member's services)"}, {"name": "introduction_phrases", "description": "Things to SAY when INTRODUCING me (exact words to use)"}, {"name": "good_referral_sources", "description": "Good referral sources (who consistently sends quality referrals)"}, {"name": "ideal_referral_characteristics", "description": "Good referral characteristics (what makes a quality referral)"}, {"name": "bad_referral_characteristics", "description": "Bad referral characteristics (what to avoid)"}, {"name": "similar_client_businesses", "description": "Businesses dealing with similar clients (contact sphere)"}, {"name": "top_3_professions_needed", "description": "Top 3 professions needed to complete referral network"}, {"name": "recent_customers", "description": "Last 10 customers or recent customer information"}, {"name": "customer_patterns", "description": "Customer patterns or types of clients served"}, {"name": "referral_patterns", "description": "How member typically gets referrals"}, {"name": "family_info", "description": "Family information"}, {"name": "pets", "description": "Pet information"}, {"name": "hobbies", "description": "Hobbies and activities"}, {"name": "city_of_residence", "description": "City of residence"}, {"name": "years_in_city", "description": "How long living in the city"}, {"name": "chapter_name", "description": "Name of the BNI chapter"}, {"name": "meeting_day", "description": "Day of the week the chapter meets"}, {"name": "meeting_time", "description": "Time when the chapter meets"}, {"name": "meeting_location", "description": "Location where the chapter meets"}, {"name": "extracted_date", "description": "Date on the dance card"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1.1, "position": [1760, 340], "id": "information-extractor", "name": "Comprehensive Information Extractor"}]}