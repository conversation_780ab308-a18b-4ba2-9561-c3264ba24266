{"createdAt": "2025-06-08T10:00:32.494Z", "updatedAt": "2025-06-08T11:47:01.000Z", "id": "wLRrus2lZJtxBlLA", "name": "Test Webhook - CORS Debug", "active": false, "isArchived": false, "nodes": [{"parameters": {"path": "test-webhook-cors", "httpMethod": "=", "options": {"allowedOrigins": "*"}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 400], "id": "webhook-test", "name": "Test Webhook"}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  success: true,\n  message: 'Webhook received successfully',\n  data: $json,\n  headers: $headers\n} }}", "options": {"responseHeaders": {"entries": [{"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"name": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 400], "id": "respond", "name": "Respond"}], "connections": {"Test Webhook": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": null, "pinData": null, "versionId": "e426d19e-4088-473b-bafe-15bef1b5e4d8", "triggerCount": 1, "shared": [{"createdAt": "2025-06-08T10:00:32.496Z", "updatedAt": "2025-06-08T10:00:32.496Z", "role": "workflow:owner", "workflowId": "wLRrus2lZJtxBlLA", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}