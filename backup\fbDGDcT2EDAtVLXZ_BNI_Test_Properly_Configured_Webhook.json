{"createdAt": "2025-06-08T11:55:03.732Z", "updatedAt": "2025-06-08T12:16:12.000Z", "id": "fbDGDcT2EDAtVLXZ", "name": "BNI Test - Properly Configured Webhook", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "bni-webhook-test", "responseMode": "responseNode", "options": {"allowedOrigins": "https://bni-presentation-generator.netlify.app,http://localhost:5173,http://localhost:5174,http://localhost:3000,http://localhost:4173"}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "id": "webhook-node", "name": "Webhook", "webhookId": "bni-webhook-test-id"}, {"parameters": {"jsCode": "// Log all incoming data for debugging\nconst webhookData = $input.item.json;\nconst headers = $input.item.json.headers || {};\nconst query = $input.item.json.query || {};\nconst body = $input.item.json.body || {};\n\nconsole.log('=== WEBHOOK DATA RECEIVED ===');\nconsole.log('Timestamp:', new Date().toISOString());\nconsole.log('Full webhook data:', JSON.stringify(webhookData, null, 2));\nconsole.log('Headers:', JSON.stringify(headers, null, 2));\nconsole.log('Query params:', JSON.stringify(query, null, 2));\nconsole.log('Body:', JSON.stringify(body, null, 2));\nconsole.log('============================');\n\n// Extract expected fields from the app\nconst expectedFields = [\n  'presentation_id',\n  'business_name',\n  'industry',\n  'products_services',\n  'target_audience',\n  'presentation_goals',\n  'initial_ideas',\n  'dance_card_url'\n];\n\nconst receivedFields = {};\nconst missingFields = [];\n\nexpectedFields.forEach(field => {\n  if (body[field]) {\n    receivedFields[field] = body[field];\n  } else {\n    missingFields.push(field);\n  }\n});\n\nreturn {\n  json: {\n    timestamp: new Date().toISOString(),\n    webhook_received: true,\n    data_structure: {\n      headers_count: Object.keys(headers).length,\n      query_params_count: Object.keys(query).length,\n      body_fields_count: Object.keys(body).length,\n      received_fields: receivedFields,\n      missing_fields: missingFields\n    },\n    raw_data: {\n      headers: headers,\n      query: query,\n      body: body\n    }\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300], "id": "code-node", "name": "Log & Analyze Data"}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"Webhook test successful - Data received\",\n  \"timestamp\": $json.timestamp,\n  \"data_analysis\": $json.data_structure,\n  \"webhook_path\": \"bni-webhook-test\",\n  \"execution_id\": $execution.id\n} }}", "options": {"responseCode": 200, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}]}}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300], "id": "respond-node", "name": "Respond to Webhook"}], "connections": {"Webhook": {"main": [[{"node": "Log & Analyze Data", "type": "main", "index": 0}]]}, "Log & Analyze Data": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": null, "pinData": null, "versionId": "78eab624-39ad-472c-9994-dbf76428bf1a", "triggerCount": 1, "shared": [{"createdAt": "2025-06-08T11:55:03.736Z", "updatedAt": "2025-06-08T11:55:03.736Z", "role": "workflow:owner", "workflowId": "fbDGDcT2EDAtVLXZ", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}