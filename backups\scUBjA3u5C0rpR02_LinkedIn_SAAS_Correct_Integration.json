{"createdAt": "2025-05-31T13:45:02.824Z", "updatedAt": "2025-05-31T13:47:28.587Z", "id": "scUBjA3u5C0rpR02", "name": "LinkedIn SAAS - Correct Integration", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "8da077f9-612d-438e-8813-72694186d247", "respond": "responseNode", "options": {"allowedOrigins": "*", "responseHeaders": {"entries": [{"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Methods", "value": "POST, OPTIONS, GET"}, {"name": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, Accept"}]}}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [840, 120], "id": "webhook-trigger", "name": "Webhook"}, {"parameters": {"toolDescription": "Use this tool to search the web for current information.", "method": "POST", "url": "https://api.tavily.com/search", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"query\": \"{searchTerm}\",\n  \"topic\": \"general\",\n  \"search_depth\": \"advanced\",\n  \"chunks_per_source\": 3,\n  \"max_results\": 2,\n  \"time_range\": null,\n  \"days\": 7,\n  \"include_answer\": true,\n  \"include_raw_content\": false,\n  \"include_images\": false,\n  \"include_image_descriptions\": false,\n  \"include_domains\": [],\n  \"exclude_domains\": []\n}", "placeholderDefinitions": {"values": [{"name": "searchTerm", "description": "What the user is searching for."}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1120, 380], "id": "tavily-search", "name": "<PERSON><PERSON>", "credentials": {"httpHeaderAuth": {"id": "R8n5b7bT7SZKoXq0", "name": "<PERSON><PERSON>"}}}, {"parameters": {"model": "openai/gpt-4.1", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [1260, 380], "id": "gpt-model", "name": "GPT-4.1", "credentials": {"openRouterApi": {"id": "yn3czriQEae3D6kG", "name": "OpenRouter account"}}}, {"parameters": {"promptType": "define", "text": "=Topic: {{ $json.body['Topic of Post'] }}\nTarget Audience: {{ $json.body['Target Audience'] }}", "options": {"systemMessage": "# LinkedIn Post Creator for Brand Wisdom Solutions\n\nYou are an AI agent specialized in creating professional, engaging LinkedIn posts for <PERSON><PERSON><PERSON><PERSON>, Founder & CEO of Brand Wisdom Solutions.\n\n## Company Context:\n- Brand Wisdom Solutions: Full-service branding and digital marketing agency in Pune, India\n- Expertise: Brand Management, AI-powered Marketing, Business Process Automation\n- Philosophy: \"Wise Brand\" framework - trust, strategic insight, market distinction\n- Track Record: 200+ successful projects, Digital Excellence Awards recognition\n- Approach: Data-driven, value-first, client-centric with measurable ROI\n\n## Instructions:\n1. ALWAYS start by using the Tavily search tool to research the topic\n2. Create a LinkedIn post that:\n   - Opens with an engaging hook or insight\n   - Maintains professional yet conversational tone\n   - Provides actionable value to readers\n   - Incorporates Brand Wisdom Solutions' perspective when relevant\n   - Uses data/examples from your research\n   - Includes 3-5 relevant hashtags\n   - Ends with a question or call-to-action\n   - Stays between 150-300 words\n\n## Output Format:\nReturn ONLY the final LinkedIn post text, nothing else. Write from <PERSON><PERSON><PERSON><PERSON>'s perspective as the agency leader.\n\n## Example Structure:\n[Hook/Insight]\n[Main content with value]\n[Brand Wisdom perspective if relevant]\n[Call to action/question]\n[Hashtags]"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1100, 100], "id": "linkedin-post-creator", "name": "LinkedIn Post Creator"}, {"parameters": {"promptType": "define", "text": "=LinkedIn Post Content:\n{{ $json.output }}", "options": {"systemMessage": "# Image Prompt Generator for LinkedIn Posts\n\nYou create professional image prompts for LinkedIn post graphics that align with Brand Wisdom Solutions' brand identity.\n\n## Objective:\nAnalyze the LinkedIn post and create a detailed image prompt for DALL-E 3 that will generate a professional, engaging visual.\n\n## Guidelines:\n- Professional B2B aesthetic\n- Modern, clean design\n- Brand Wisdom Solutions quality standards\n- Suitable for LinkedIn audience\n- Support the post's key message\n\n## Visual Elements to Consider:\n- Business infographics\n- Data visualizations\n- Abstract concepts\n- Professional icons\n- Clean typography layouts\n- Modern color schemes (blues, whites, grays with accent colors)\n\n## Output:\nReturn ONLY the image prompt text, no quotes or explanations.\n\n## Example Format:\nA modern business infographic showing [specific visual elements] with clean gradient background from [color] to [color], professional layout with [specific design elements], minimalist design suitable for LinkedIn business audience."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1540, 100], "id": "image-prompt-generator", "name": "Image Prompt Generator"}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/generations", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "dall-e-3"}, {"name": "prompt", "value": "={{ $json.output }}"}, {"name": "size", "value": "1024x1024"}, {"name": "quality", "value": "standard"}, {"name": "response_format", "value": "b64_json"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1980, 100], "id": "dalle-image-generator", "name": "DALL-E Image Generator", "credentials": {"httpHeaderAuth": {"id": "R8n5b7bT7SZKoXq0", "name": "<PERSON><PERSON>"}}}, {"parameters": {"operation": "toBinary", "sourceProperty": "data[0].b64_json", "options": {"fileName": "linkedin-post-image.png", "mimeType": "image/png"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [2180, 100], "id": "convert-image", "name": "Convert to Image"}, {"parameters": {"sendTo": "={{ $('Webhook').item.json.body.Email }}", "subject": "Your LinkedIn Post is Ready! 🎉", "message": "=<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n  <h2 style=\"color: #0066cc; margin-bottom: 20px;\">Your LinkedIn Post is Ready!</h2>\n  \n  <p>Hi there!</p>\n  \n  <p>Your professional LinkedIn post has been generated successfully. Here's your content:</p>\n  \n  <div style=\"background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #0066cc; white-space: pre-wrap;\">{{ $('LinkedIn Post Creator').item.json.output }}</div>\n  \n  <div style=\"background-color: #e8f4fd; padding: 15px; border-radius: 6px; margin: 20px 0;\">\n    <p style=\"margin: 0; font-weight: bold; color: #0066cc;\">Post Details:</p>\n    <p style=\"margin: 5px 0;\"><strong>Topic:</strong> {{ $('Webhook').item.json.body['Topic of Post'] }}</p>\n    <p style=\"margin: 5px 0;\"><strong>Target Audience:</strong> {{ $('Webhook').item.json.body['Target Audience'] }}</p>\n  </div>\n  \n  <p>The custom image for your post is attached to this email. Simply download it and use it along with your post on LinkedIn!</p>\n  \n  <div style=\"background-color: #f0f8ff; padding: 15px; border-radius: 6px; margin: 20px 0;\">\n    <p style=\"margin: 0; font-size: 14px; color: #666;\">💡 <strong>Pro Tip:</strong> Copy the post text, upload the image to LinkedIn, paste the text as your caption, and you're ready to engage your audience!</p>\n  </div>\n  \n  <hr style=\"margin: 30px 0; border: none; border-top: 1px solid #ddd;\">\n  \n  <p style=\"color: #666; font-size: 14px; text-align: center;\">\n    Generated by <strong>Brand Wisdom Solutions</strong> LinkedIn Post Creator<br>\n    Need help with your brand strategy? <a href=\"https://brandwisdomsolutions.com\" style=\"color: #0066cc;\">Contact us</a>\n  </p>\n</div>", "options": {"appendAttribution": false, "attachmentsUi": {"attachmentsBinary": [{"property": "data"}]}}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [2440, 100], "id": "send-email", "name": "Send Email", "credentials": {"gmailOAuth2": {"id": "cIW854HS90G0DCor", "name": "Gmail account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"LinkedIn post generated successfully!\",\n  \"email\": \"{{ $('Webhook').item.json.body.Email }}\",\n  \"topic\": \"{{ $('Webhook').item.json.body['Topic of Post'] }}\",\n  \"targetAudience\": \"{{ $('Webhook').item.json.body['Target Audience'] }}\",\n  \"timestamp\": \"{{ new Date().toISOString() }}\",\n  \"data\": {\n    \"post\": \"{{ $('LinkedIn Post Creator').item.json.output }}\",\n    \"imageGenerated\": true,\n    \"processingTime\": \"{{ Math.round((new Date() - new Date($('Webhook').item.json.timestamp || new Date())) / 1000) }}s\"\n  }\n}", "options": {"responseCode": 200, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Methods", "value": "POST, OPTIONS, GET"}, {"name": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, Accept"}]}}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [2640, 100], "id": "webhook-response", "name": "Webhook Response"}], "connections": {"Webhook": {"main": [[{"node": "LinkedIn Post Creator", "type": "main", "index": 0}]]}, "Tavily Search": {"ai_tool": [[{"node": "LinkedIn Post Creator", "type": "ai_tool", "index": 0}]]}, "GPT-4.1": {"ai_languageModel": [[{"node": "LinkedIn Post Creator", "type": "ai_languageModel", "index": 0}, {"node": "Image Prompt Generator", "type": "ai_languageModel", "index": 0}]]}, "LinkedIn Post Creator": {"main": [[{"node": "Image Prompt Generator", "type": "main", "index": 0}]]}, "Image Prompt Generator": {"main": [[{"node": "DALL-E Image Generator", "type": "main", "index": 0}]]}, "DALL-E Image Generator": {"main": [[{"node": "Convert to Image", "type": "main", "index": 0}]]}, "Convert to Image": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "Send Email": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": null, "pinData": null, "versionId": "c15a87b3-08dc-4695-8026-d87cc0fa522c", "triggerCount": 1, "shared": [{"createdAt": "2025-05-31T13:45:02.831Z", "updatedAt": "2025-05-31T13:45:02.831Z", "role": "workflow:owner", "workflowId": "scUBjA3u5C0rpR02", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}