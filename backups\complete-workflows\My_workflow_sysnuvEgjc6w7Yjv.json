{"createdAt": "2025-05-30T21:17:00.105Z", "updatedAt": "2025-05-30T21:26:55.000Z", "id": "sysnuvEgjc6w7Yjv", "name": "My workflow", "active": false, "isArchived": false, "nodes": [{"parameters": {"toolDescription": "Use this tool to search the web. ", "method": "POST", "url": "https://api.tavily.com/search", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"query\": \"{searchTerm}\",\n  \"topic\": \"general\",\n  \"search_depth\": \"advanced\",\n  \"chunks_per_source\": 3,\n  \"max_results\": 1,\n  \"time_range\": null,\n  \"days\": 7,\n  \"include_answer\": true,\n  \"include_raw_content\": false,\n  \"include_images\": false,\n  \"include_image_descriptions\": false,\n  \"include_domains\": [],\n  \"exclude_domains\": []\n}", "placeholderDefinitions": {"values": [{"name": "searchTerm", "description": "What the user is searching for. "}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1120, 380], "id": "7d3491bb-c31a-4ba5-98a1-3e48ec30d69a", "name": "<PERSON><PERSON>"}, {"parameters": {"model": "openai/gpt-4.1", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [1260, 380], "id": "9cfa266a-cf2a-4cba-9b9e-8fe337f64f17", "name": "GPT_4.1"}, {"parameters": {"person": "=", "shareMediaCategory": "IMAGE", "additionalFields": {}}, "type": "n8n-nodes-base.linkedIn", "typeVersion": 1, "position": [2640, 580], "id": "704ba5ee-6ef5-4d81-b41c-f12cc83c559f", "name": "LinkedIn", "disabled": true}, {"parameters": {"promptType": "define", "text": "=Topic of Post: {{ $json['Topic of Post'] }}\n\nTarget Audience: {{ $json['Target Audience'] }}", "options": {"systemMessage": "=# Overview\nYou are an AI agent specialized in creating professional, educational, and engaging LinkedIn posts based on any topic provided by the user.\n\n## Objectives:\nAlways begin by conducting a real-time search using the Tavily tool to gather the most accurate, up-to-date information on the topic. The post should be written to appeal to the provided target audience.\n\nBased on your research, generate a well-structured LinkedIn post that:\n- Starts with an engaging hook\n- Professional in tone\n- Clear and easy to read\n- Educational and insightful\n- Light on emojis (use only when highly relevant and minimal)\n- Includes proper source attribution (e.g., \"according to [source]\")\n- Contains relevant hashtags to improve visibility\n- Ends with a clear call to action (e.g., asking for thoughts, feedback, or shares)\n\n## Output Instructions:\n- Your ONLY output should be the final LinkedIn post text.\n- Do not include explanations, notes, or anything beyond the post itself.\n\n## Example Workflow:\n1) Receive a topic (e.g., \"The ROI of warehouse automation\")\n2) Use Tavily to search and gather recent information or case studies\n3) Draft a LinkedIn post using that research\n4) Format it with source citations, clean structure, optional hashtags, and a call to action"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1100, 100], "id": "62308f09-ac8c-42dd-95ef-bac18be62c86", "name": "LinkedIn Post Agent"}, {"parameters": {"promptType": "define", "text": "=LinkedIn Post: \n{{ $json.output }}", "options": {"systemMessage": "=# Overview\nYou are an AI agent that transforms LinkedIn posts into visual prompt descriptions for generating graphic marketing materials. These visuals are designed to be paired with the post on LinkedIn, helping communicate the message in a visually engaging, brand-aligned way.\n\n## Objective:\n- Read and analyze the given LinkedIn post.\n- Identify the main message, insight, or takeaway from the post.\n- Create a clear and compelling graphic prompt that can be used with a text-to-image generator.\n- The result should be a marketing-style graphic — not a literal scene or hyperrealistic photo — that:\n1) Visually supports or illustrates the key idea of the post\n2) Looks appropriate for use in a professional LinkedIn feed\n3) Feels polished, modern, and engaging\n\n## Output Instructions:\n- Output only the final image prompt. Do not output quotation marks.\n- Do not repeat or rephrase the LinkedIn post.\n- Do not add any explanations or extra content — just the image prompt.\n- Never leave things blank like \"Header area reserved for customizable callout text\"\n- Output numeric stats when available in the original post\n\n## Style Guidelines:\n- Think like a brand designer or marketing creative.\n- Visuals may include: text, charts, icons, abstract shapes, overlays, modern illustrations, motion-like effects, bold typography elements (described, not rendered), or metaphorical concepts.\n- You can mention layout suggestions (e.g., \"split screen design,\" \"header with bold title and subtle background illustration\").\n- Assume the output will be generated using AI image tools — your prompt should guide those tools effectively.\n\n## Example Prompt Format:\nA modern flat-style graphic showing a human brain connected to mechanical gears, representing the fusion of AI and automation. Minimalist background, soft gradients, clean sans-serif text placement space at the top.\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1540, 100], "id": "c4144c3f-798d-4ddf-a663-a88819032184", "name": "Image Prompt Agent"}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/generations", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR API KEY"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "={{ $json.output }}"}, {"name": "size", "value": "1024x1024"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1980, 100], "id": "17c09dcf-924c-4255-8153-8bf9f4a62432", "name": "Generate Image"}, {"parameters": {"operation": "toBinary", "sourceProperty": "data[0].b64_json", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [2180, 100], "id": "49b3bf86-573f-46b5-b0b5-7163aabe4426", "name": "Convert to <PERSON>ary"}, {"parameters": {"sendTo": "={{ $('On form submission').item.json.Email }}", "subject": "Your LinkedIn Post is Here!", "emailType": "text", "message": "=Here you go Nate!\n\n\n{{ $('LinkedIn Post Agent').item.json.output }}", "options": {"appendAttribution": false, "attachmentsUi": {"attachmentsBinary": [{}]}}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [2440, 100], "id": "8246ec97-c195-4d66-aa46-a84a2933ac0e", "name": "Send Post", "webhookId": "20f8ef41-7183-41f1-9104-a6a042b807ce"}, {"parameters": {"formTitle": "LinkedIn Post Generator", "formDescription": "Fill out these fields and you'll have a full LinkedIn post ready to go in a minute. ", "formFields": {"values": [{"fieldLabel": "Email", "fieldType": "email", "placeholder": "<EMAIL>", "requiredField": true}, {"fieldLabel": "Topic of Post", "placeholder": "Robots", "requiredField": true}, {"fieldLabel": "Target Audience", "requiredField": true}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [820, 100], "id": "e4c51015-5f5c-4030-a5d0-a3dedb907317", "name": "On form submission", "webhookId": "359fa996-5531-40ae-be37-4a6e136952e2"}], "connections": {"Tavily": {"ai_tool": [[{"node": "LinkedIn Post Agent", "type": "ai_tool", "index": 0}]]}, "GPT_4.1": {"ai_languageModel": [[{"node": "LinkedIn Post Agent", "type": "ai_languageModel", "index": 0}, {"node": "Image Prompt Agent", "type": "ai_languageModel", "index": 0}]]}, "LinkedIn Post Agent": {"main": [[{"node": "Image Prompt Agent", "type": "main", "index": 0}]]}, "Image Prompt Agent": {"main": [[{"node": "Generate Image", "type": "main", "index": 0}]]}, "Generate Image": {"main": [[{"node": "Convert to <PERSON>ary", "type": "main", "index": 0}]]}, "Convert to Binary": {"main": [[{"node": "Send Post", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "LinkedIn Post Agent", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": null, "pinData": {}, "versionId": "df716e9a-f4de-4076-9a7d-7a4e20ec14d3", "triggerCount": 0, "shared": [{"createdAt": "2025-05-30T21:17:00.109Z", "updatedAt": "2025-05-30T21:17:00.109Z", "role": "workflow:owner", "workflowId": "sysnuvEgjc6w7Yjv", "projectId": "jncwk4CjUI6FYXvX"}], "tags": []}