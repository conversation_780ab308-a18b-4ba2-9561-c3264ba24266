{"createdAt": "2025-06-08T11:45:04.639Z", "updatedAt": "2025-06-08T11:47:03.000Z", "id": "CgkkDjPVNM01fAoT", "name": "BNI Simple Webhook Test", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "bni-simple-test", "options": {"allowedOrigins": "*"}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [220, 300], "id": "e8d512d8-26d7-41cf-a7f2-4ac431e89cb0", "name": "Webhook", "webhookId": "bni-simple-test-webhook"}], "connections": {}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": null, "pinData": null, "versionId": "2c757649-8d75-40bc-a40a-b20cfb17411b", "triggerCount": 1, "shared": [{"createdAt": "2025-06-08T11:45:04.640Z", "updatedAt": "2025-06-08T11:45:04.640Z", "role": "workflow:owner", "workflowId": "CgkkDjPVNM01fAoT", "projectId": "jncwk4CjUI6FYXvX"}], "tags": []}