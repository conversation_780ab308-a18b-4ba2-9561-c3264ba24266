{"createdAt": "2025-06-08T16:38:39.326Z", "updatedAt": "2025-06-08T19:10:59.000Z", "id": "cOXLUxmPm8LFRkVR", "name": "BNI Document Processor v3 - Mistral OCR Test", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "bni-document-processor-v3-mistral-test", "responseMode": "responseNode", "options": {"allowedOrigins": "https://bni-presentation-generator.netlify.app,http://localhost:5173,http://localhost:5174,http://localhost:3000,http://localhost:4173"}}, "name": "Document Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [220, 340], "id": "webhook-node", "webhookId": "bni-doc-v3-mistral-test"}, {"parameters": {"jsCode": "// Comprehensive validation and data preparation\nconst payload = $input.item.json.body || $input.item.json;\n\nconsole.log('=== MISTRAL OCR PROCESSING REQUEST ===');\nconsole.log('Timestamp:', new Date().toISOString());\nconsole.log('Payload:', JSON.stringify(payload, null, 2));\n\n// Initialize variables\nlet document_url;\nlet presentation_id;\nlet filename = 'document'; // Default filename\n\n// Check for document URL in multiple possible locations\nif (payload.dance_card_url) {\n  document_url = payload.dance_card_url;\n  presentation_id = payload.presentation_id;\n  console.log('Processing presentation dance card:', document_url);\n} else if (payload.document_url) {\n  document_url = payload.document_url;\n  presentation_id = payload.presentation_id || null;\n} else {\n  throw new Error('Missing required field: document_url or dance_card_url');\n}\n\n// Extract filename with robust URL parsing\ntry {\n  const urlObj = new URL(document_url);\n  const pathParts = urlObj.pathname.split('/');\n  const lastSegment = pathParts[pathParts.length - 1];\n  \n  if (lastSegment) {\n    // Remove query parameters and decode URI\n    filename = decodeURIComponent(lastSegment.split('?')[0]);\n  }\n} catch (e) {\n  console.warn('Could not parse URL, attempting basic extraction:', e.message);\n  // Fallback: basic string manipulation\n  const urlParts = document_url.split('/');\n  const lastPart = urlParts[urlParts.length - 1];\n  if (lastPart) {\n    filename = lastPart.split('?')[0];\n  }\n}\n\n// Extract and validate file extension\nconst fileExtension = filename.split('.').pop().toLowerCase();\nconst supportedTypes = ['pdf', 'jpg', 'jpeg', 'png', 'docx', 'pptx'];\n\nif (!supportedTypes.includes(fileExtension)) {\n  throw new Error(`Unsupported file type: ${fileExtension}. Supported types: ${supportedTypes.join(', ')}`);\n}\n\n// Determine file type category\nconst fileType = fileExtension === 'pdf' ? 'pdf' : \n                ['jpg', 'jpeg', 'png'].includes(fileExtension) ? 'image' : \n                'document';\n\n// Prepare output data\nreturn {\n  json: {\n    document_url: document_url,\n    presentation_id: presentation_id,\n    filename: filename,\n    file_extension: fileExtension,\n    file_type: fileType,\n    timestamp: new Date().toISOString(),\n    original_payload: payload,\n    // Preserve presentation context if available\n    presentation_data: payload.dance_card_url ? {\n      business_name: payload.business_name || '',\n      industry: payload.industry || '',\n      products_services: payload.products_services || '',\n      target_audience: payload.target_audience || '',\n      presentation_goals: payload.presentation_goals || '',\n      initial_ideas: payload.initial_ideas || ''\n    } : null,\n    // Metadata for tracking\n    processing_metadata: {\n      workflow: 'mistral-ocr-test',\n      version: '3.0',\n      environment: payload.webhookUrl?.includes('webhook-test') ? 'test' : 'production'\n    }\n  }\n};"}, "name": "Validate & Prepare Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, 340], "id": "validate-node"}, {"parameters": {"url": "={{ $json.document_url }}", "options": {"response": {"response": {"responseFormat": "file"}}, "timeout": 30000}}, "name": "Download Document", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [660, 340], "id": "download-node"}, {"parameters": {"method": "POST", "url": "https://api.mistral.ai/v1/files", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "data"}, {"name": "purpose", "value": "ocr"}]}, "options": {}}, "name": "Mistral - Upload File", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [880, 340], "id": "mistral-upload", "credentials": {"httpHeaderAuth": {"id": "gqJibJ5zG59mTjvy", "name": "Mistral OCR"}}}, {"parameters": {"url": "=https://api.mistral.ai/v1/files/{{ $json.id }}/url", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "name": "Mistral - Get Signed URL", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1100, 340], "id": "mistral-signed-url", "credentials": {"httpHeaderAuth": {"id": "gqJibJ5zG59mTjvy", "name": "Mistral OCR"}}}, {"parameters": {"method": "POST", "url": "https://api.mistral.ai/v1/ocr", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"mistral-ocr-latest\",\n  \"document\": {\n    \"type\": \"document_url\",\n    \"document_url\": \"{{ $json.url }}\"\n  },\n  \"include_image_base64\": true\n}", "options": {"response": {"response": {"responseFormat": "text", "outputPropertyName": "ocr_output.json"}}}}, "name": "Mistral - OCR Process", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1320, 340], "id": "mistral-ocr", "credentials": {"httpHeaderAuth": {"id": "gqJibJ5zG59mTjvy", "name": "Mistral OCR"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.success }}", "value2": true}]}}, "name": "IF - Check Success", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2356, 340], "id": "check-success"}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {"responseCode": 200, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Processing-Time", "value": "={{ Date.now() - new Date($('Validate & Prepare Data').item.json.timestamp).getTime() }}ms"}]}}}, "name": "Respond Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2576, 240], "id": "respond-success"}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {"responseCode": 400, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Processing-Time", "value": "={{ Date.now() - new Date($('Validate & Prepare Data').item.json.timestamp).getTime() }}ms"}]}}}, "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2576, 440], "id": "respond-error"}, {"parameters": {"text": "={{ $json['ocr_output.json'] }}", "attributes": {"attributes": [{"name": "Name", "description": "Name of the person", "required": true}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1.1, "position": [1540, 340], "id": "742a1906-857b-45d4-a1a6-49edaaed5f4a", "name": "Information Extractor"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [1580, 560], "id": "9c94af4d-5682-4756-a538-8195cbc5c330", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "yn3czriQEae3D6kG", "name": "OpenRouter account"}}}], "connections": {"Document Webhook": {"main": [[{"node": "Validate & Prepare Data", "type": "main", "index": 0}]]}, "Validate & Prepare Data": {"main": [[{"node": "Download Document", "type": "main", "index": 0}]]}, "Download Document": {"main": [[{"node": "Mistral - Upload File", "type": "main", "index": 0}]]}, "Mistral - Upload File": {"main": [[{"node": "Mistral - Get Signed URL", "type": "main", "index": 0}]]}, "Mistral - Get Signed URL": {"main": [[{"node": "Mistral - OCR Process", "type": "main", "index": 0}]]}, "Mistral - OCR Process": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}]]}, "IF - Check Success": {"main": [[{"node": "Respond Success", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "Information Extractor", "type": "ai_languageModel", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "320e1099-d349-4919-a2fb-7fda0a58457b", "triggerCount": 0, "shared": [{"createdAt": "2025-06-08T16:38:39.328Z", "updatedAt": "2025-06-08T16:38:39.328Z", "role": "workflow:owner", "workflowId": "cOXLUxmPm8LFRkVR", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}