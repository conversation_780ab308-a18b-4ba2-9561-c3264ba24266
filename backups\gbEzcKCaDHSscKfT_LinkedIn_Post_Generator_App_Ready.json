{"createdAt": "2025-05-31T10:34:14.173Z", "updatedAt": "2025-05-31T10:42:01.097Z", "id": "gbEzcKCaDHSscKfT", "name": "LinkedIn Post Generator - App Ready", "active": false, "isArchived": false, "nodes": [{"parameters": {"path": "linkedin-post-generator", "httpMethod": "POST", "options": {"rawBody": false, "responseData": "allEntries", "responseMode": "responseNode"}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [200, 300], "id": "webhook-trigger", "name": "Webhook - App Integration"}, {"parameters": {"toolDescription": "Use this tool to search the web.", "method": "POST", "url": "https://api.tavily.com/search", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"query\": \"{searchTerm}\",\n  \"topic\": \"general\",\n  \"search_depth\": \"advanced\",\n  \"chunks_per_source\": 3,\n  \"max_results\": 1,\n  \"time_range\": null,\n  \"days\": 7,\n  \"include_answer\": true,\n  \"include_raw_content\": false,\n  \"include_images\": false,\n  \"include_image_descriptions\": false,\n  \"include_domains\": [],\n  \"exclude_domains\": []\n}", "placeholderDefinitions": {"values": [{"name": "searchTerm", "description": "What the user is searching for."}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [500, 500], "id": "tavily-tool", "name": "<PERSON><PERSON>", "credentials": {"httpHeaderAuth": {"id": "R8n5b7bT7SZKoXq0", "name": "<PERSON><PERSON>"}}}, {"parameters": {"model": "openai/gpt-4.1", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [700, 500], "id": "gpt-model", "name": "GPT-4 Model", "credentials": {"openRouterApi": {"id": "yn3czriQEae3D6kG", "name": "OpenRouter account"}}}, {"parameters": {"promptType": "define", "text": "=Topic of Post: {{ $json['Topic of Post'] }}\n\nTarget Audience: {{ $json['Target Audience'] }}", "options": {"systemMessage": "# Overview\nYou are an AI agent specialized in creating professional, educational, and engaging LinkedIn posts based on any topic provided by the user.\n\n## Objectives:\nAlways begin by conducting a real-time search using the Tavily tool to gather the most accurate, up-to-date information on the topic. The post should be written to appeal to the provided target audience.\n\nBased on your research, generate a well-structured LinkedIn post that:\n- Starts with an engaging hook\n- Professional in tone\n- Clear and easy to read\n- Educational and insightful\n- Light on emojis (use only when highly relevant and minimal)\n- Includes proper source attribution (e.g., \"according to [source]\")\n- Contains relevant hashtags to improve visibility\n- Ends with a clear call to action (e.g., asking for thoughts, feedback, or shares)\n\n## Output Instructions:\n- Your ONLY output should be the final LinkedIn post text.\n- Do not include explanations, notes, or anything beyond the post itself.\n\n## Example Workflow:\n1) Receive a topic (e.g., \"The ROI of warehouse automation\")\n2) Use Tavily to search and gather recent information or case studies\n3) Draft a LinkedIn post using that research\n4) Format it with source citations, clean structure, optional hashtags, and a call to action"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [500, 300], "id": "linkedin-agent", "name": "LinkedIn Post Agent"}, {"parameters": {"promptType": "define", "text": "=LinkedIn Post: \n{{ $json.output }}", "options": {"systemMessage": "# Overview\nYou are an AI agent that transforms LinkedIn posts into visual prompt descriptions for generating graphic marketing materials. These visuals are designed to be paired with the post on LinkedIn, helping communicate the message in a visually engaging, brand-aligned way.\n\n## Objective:\n- Read and analyze the given LinkedIn post.\n- Identify the main message, insight, or takeaway from the post.\n- Create a clear and compelling graphic prompt that can be used with a text-to-image generator.\n- The result should be a marketing-style graphic — not a literal scene or hyperrealistic photo — that:\n1) Visually supports or illustrates the key idea of the post\n2) Looks appropriate for use in a professional LinkedIn feed\n3) Feels polished, modern, and engaging\n\n## Output Instructions:\n- Output only the final image prompt. Do not output quotation marks.\n- Do not repeat or rephrase the LinkedIn post.\n- Do not add any explanations or extra content — just the image prompt.\n- Never leave things blank like \"Header area reserved for customizable callout text\"\n- Output numeric stats when available in the original post\n\n## Style Guidelines:\n- Think like a brand designer or marketing creative.\n- Visuals may include: text, charts, icons, abstract shapes, overlays, modern illustrations, motion-like effects, bold typography elements (described, not rendered), or metaphorical concepts.\n- You can mention layout suggestions (e.g., \"split screen design,\" \"header with bold title and subtle background illustration\").\n- Assume the output will be generated using AI image tools — your prompt should guide those tools effectively.\n\n## Example Prompt Format:\nA modern flat-style graphic showing a human brain connected to mechanical gears, representing the fusion of AI and automation. Minimalist background, soft gradients, clean sans-serif text placement space at the top."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [900, 300], "id": "image-prompt-agent", "name": "Image Prompt Agent"}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/generations", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "dall-e-3"}, {"name": "prompt", "value": "={{ $json.output }}"}, {"name": "size", "value": "1024x1024"}, {"name": "quality", "value": "standard"}, {"name": "n", "value": "1"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1300, 300], "id": "generate-image", "name": "Generate Image", "credentials": {"httpHeaderAuth": {"id": "XJVZYupvyBODoSGx", "name": "Header Auth account"}}}, {"parameters": {"sendTo": "={{ $('Webhook - App Integration').item.json.Email }}", "subject": "Your LinkedIn Post is Ready! 🚀", "emailType": "html", "message": "=<h2>Your LinkedIn Post is Ready!</h2>\n<p>Hello!</p>\n<p>Your LinkedIn post about <strong>{{ $('Webhook - App Integration').item.json['Topic of Post'] }}</strong> for <strong>{{ $('Webhook - App Integration').item.json['Target Audience'] }}</strong> has been generated successfully.</p>\n<h3>Your LinkedIn Post:</h3>\n<div style=\"background-color: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n{{ $('LinkedIn Post Agent').item.json.output }}\n</div>\n<p>We've also generated a custom image for your post which is attached to this email.</p>\n<p>Best regards,<br>LinkedIn Post Generator</p>", "options": {"appendAttribution": false, "attachmentsUi": {"attachmentsBinary": [{"property": "data"}]}}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1700, 300], "id": "send-email", "name": "Send Email", "credentials": {"gmailOAuth2": {"id": "cIW854HS90G0DCor", "name": "Gmail account"}}}, {"parameters": {"options": {"responseCode": 200, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Methods", "value": "POST, OPTIONS"}, {"name": "Access-Control-Allow-Headers", "value": "Content-Type"}]}}, "respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"LinkedIn post generated successfully\",\n  \"data\": {\n    \"email\": \"{{ $('Webhook - App Integration').item.json.Email }}\",\n    \"topic\": \"{{ $('Webhook - App Integration').item.json['Topic of Post'] }}\",\n    \"targetAudience\": \"{{ $('Webhook - App Integration').item.json['Target Audience'] }}\",\n    \"post\": \"{{ $('LinkedIn Post Agent').item.json.output }}\",\n    \"imageUrl\": \"{{ $('Generate Image').item.json.data[0].url }}\",\n    \"timestamp\": \"{{ $now.toISO() }}\"\n  }\n}"}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1900, 300], "id": "respond-webhook", "name": "Respond to Webhook"}, {"parameters": {"operation": "toBinary", "sourceProperty": "data[0].url", "options": {"fileName": "linkedin-post-image.png", "mimeType": "image/png"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1500, 300], "id": "convert-to-binary", "name": "Convert Image to Binary"}], "connections": {"Webhook - App Integration": {"main": [[{"node": "LinkedIn Post Agent", "type": "main", "index": 0}]]}, "Tavily Search": {"ai_tool": [[{"node": "LinkedIn Post Agent", "type": "ai_tool", "index": 0}]]}, "GPT-4 Model": {"ai_languageModel": [[{"node": "LinkedIn Post Agent", "type": "ai_languageModel", "index": 0}, {"node": "Image Prompt Agent", "type": "ai_languageModel", "index": 0}]]}, "LinkedIn Post Agent": {"main": [[{"node": "Image Prompt Agent", "type": "main", "index": 0}]]}, "Image Prompt Agent": {"main": [[{"node": "Generate Image", "type": "main", "index": 0}]]}, "Generate Image": {"main": [[{"node": "Convert Image to Binary", "type": "main", "index": 0}]]}, "Convert Image to Binary": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "Send Email": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": null, "pinData": null, "versionId": "da1f757e-297e-4b5a-99e7-bfbc4f1c23ac", "triggerCount": 1, "shared": [{"createdAt": "2025-05-31T10:34:14.175Z", "updatedAt": "2025-05-31T10:34:14.175Z", "role": "workflow:owner", "workflowId": "gbEzcKCaDHSscKfT", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}