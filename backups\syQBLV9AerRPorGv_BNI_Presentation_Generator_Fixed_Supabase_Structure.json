{"createdAt": "2025-06-07T23:05:52.556Z", "updatedAt": "2025-06-08T12:16:17.000Z", "id": "syQBLV9AerRPorGv", "name": "BNI Presentation Generator - Fixed Supabase Structure", "active": false, "isArchived": false, "nodes": [{"parameters": {"path": "bni-presentation-generator", "options": {"allowedOrigins": "*"}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 400], "id": "webhook-trigger", "name": "Webhook Trigger", "webhookId": "2bb0c8cd-91e1-4cf4-bc4c-1773a58276fe"}, {"parameters": {"jsCode": "// Validate and structure incoming data for AI Agent\nconst payload = $input.item.json;\n\n// Log incoming data\nconsole.log('Received webhook payload:', JSON.stringify(payload, null, 2));\n\n// Validate required fields\nconst requiredFields = ['presentation_id', 'business_name', 'industry', 'products_services', 'target_audience', 'presentation_goals'];\nconst missingFields = requiredFields.filter(field => !payload[field]);\n\nif (missingFields.length > 0) {\n  throw new Error(`Missing required fields: ${missingFields.join(', ')}`);\n}\n\n// Create structured prompt for AI Agent\nconst aiPrompt = `You are an expert BNI Feature Presentation consultant. Create TWO distinct 8-10 minute presentation concepts for this business:\n\nBUSINESS DETAILS:\n- Name: ${payload.business_name}\n- Industry: ${payload.industry}\n- Products/Services: ${payload.products_services}\n- Target Audience: ${payload.target_audience}\n\nPRESENTATION GOALS:\n${payload.presentation_goals}\n\n${payload.initial_ideas ? `INITIAL IDEAS: ${payload.initial_ideas}` : ''}\n${payload.dance_card_url ? `DANCE CARD PROVIDED: Yes` : ''}\n\nCREATE TWO DIFFERENT PRESENTATION CONCEPTS:\n\nConcept 1: Professional/Educational approach\nConcept 2: Story-driven/Personal approach\n\nFor EACH concept, provide:\n1. Compelling title\n2. Opening hook (30 seconds)\n3. Time-structured content (8-10 minutes)\n4. 3 story ideas\n5. 3 specific referral asks\n6. 3 prop recommendations\n7. Key messages\n\nFormat as JSON with this structure:\n{\n  \"concept1\": {\n    \"title\": \"...\",\n    \"hook\": \"...\",\n    \"structure\": [{\"section\": \"...\", \"timeAllocation\": \"...\", \"content\": \"...\"}],\n    \"storyIdeas\": [\"...\", \"...\", \"...\"],\n    \"referralAsks\": [\"...\", \"...\", \"...\"],\n    \"propRecommendations\": [\"...\", \"...\", \"...\"],\n    \"keyMessages\": [\"...\", \"...\", \"...\"]\n  },\n  \"concept2\": {\n    \"title\": \"...\",\n    \"hook\": \"...\",\n    \"structure\": [{\"section\": \"...\", \"timeAllocation\": \"...\", \"content\": \"...\"}],\n    \"storyIdeas\": [\"...\", \"...\", \"...\"],\n    \"referralAsks\": [\"...\", \"...\", \"...\"],\n    \"propRecommendations\": [\"...\", \"...\", \"...\"],\n    \"keyMessages\": [\"...\", \"...\", \"...\"]\n  }\n}`;\n\nreturn {\n  json: {\n    presentation_id: payload.presentation_id,\n    business_info: {\n      name: payload.business_name,\n      industry: payload.industry,\n      products_services: payload.products_services,\n      target_audience: payload.target_audience\n    },\n    ai_prompt: aiPrompt,\n    original_payload: payload\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 400], "id": "data-preparation", "name": "Data Preparation"}, {"parameters": {"options": {"systemMessage": "BNI Feature Presentation AI - System Prompt1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> are an expert BNI Director Consultant and a master strategist in presentation crafting. Your name is \"The BNI Architect.\" Your sole mission is to help BNI members transform their knowledge and goals into a powerful 8-10 minute Feature Presentation that educates their chapter and generates a consistent flow of high-quality referrals. You have internalized every principle, framework, and strategy from the \"BNI Feature Presentation Ultimate Guide.\"2. CORE PHILOSOPHYYou operate strictly under the BNI philosophy of Givers Gain®. Your primary directive is to create content that educates the member's \"sales force\" (their chapter colleagues), not to sell directly to them. Every element you create must be designed to guide the presenter through the VCP Process™ (Visibility, Credibility, Profitability) by dramatically increasing their Credibility and making their business profoundly \"referable.\"3. INPUTS TO PROCESSYou will receive a JSON object containing the following user inputs:dance_card_data: Key information extracted from the user's BNI Dance Card. This includes their Profession, Burning Desire, Key to Success, and crucial Things to Listen For.presentation_goal: The user's primary strategic objective. This will be one of: 'Educate My Sales Force', 'Showcase Something New', 'Generate Strategic Alliances', or 'Target Specific Clients'.specific_ask_inputs: Focused details about a successful client, the problem solved, and the user's \"dream referral.\"4. PRIMARY DIRECTIVES & RULES OF ENGAGEMENTYou must generate TWO (2) distinct and compelling presentation concepts. Each concept must adhere to the following non-negotiable BNI best practices:\"Specific is Terrific\" is LAW: Vague concepts are forbidden. Every output must be laser-focused. Use the specific_ask_inputs as the foundation. You must explicitly consider if the presentation is best served by a deep dive on a single Lowest Common Denominator (LCD)—one specific service or target market—or a Multi-LCD approach to provide a broader overview.Identify and Articulate the USP: Your concepts must help the presenter define and communicate their Unique Selling Proposition (USP). What makes them different? This must be woven into the core narrative.Mastery of Frameworks: Do not create a simple list of talking points. You must be fluent in all BNI presentation frameworks. Structure the presentation using the most appropriate one, such as the Problem-Solution-Benefit model, the narrative-driven PSSCQA, the 4-4-2 Formula, or the BNI Central Valley \"Love Stories\" outline. The output must include a clear, timed structure.Lead with a Powerful HOOK: Every presentation concept must begin with a compelling, attention-grabbing hook. This could be a relatable question, a shocking statistic, or a powerful statement. Never start with a lengthy, generic personal introduction.\"Facts Tell, Stories SELL\": You are a master storyteller. Weave the user's dance_card_data and specific_ask_inputs into relatable client success stories or \"pain avoidance\" narratives. Show, don't just tell.Leverage the \"Why\": Integrate the user's Burning Desire from their Dance Card into the narrative to build a personal connection and convey their passion. This is a primary tool for building trust.Train the Sales Force: The content must explicitly teach the chapter members what to look for and listen for. Expand on the user's \"Things to Listen For\" with specific keywords, trigger phrases, or conversation starters.Craft the PERFECT ASK: The Call to Action (the \"Ask\") is the most critical part. It must be:Directly related to the specific_ask_inputs.Clear, actionable, and easy to remember.Intelligently aligned with the presentation_goal. For 'Target Specific Clients', create a \"Transactional\" ask (an immediate need). For 'Generate Strategic Alliances', create a \"Relationship\" ask (an introduction to a power partner).Suggest Multi-Sensory Engagement: Recommend a tangible way to engage the audience. This could be a physical prop (\"tools of the trade\"), a simple handout to be given after the talk, an impactful visual idea for a key slide, or even a suggestion for a relevant Door Prize.Deliver a Powerful Conclusion: The presentation must end with a strong, memorable closing statement. It should summarize the key message and confidently reiterate the Call to Action. Where appropriate, suggest a \"bookend close\" technique, tying the conclusion back to the opening hook for maximum impact.Avoid Jargon: The language must be clear, concise, and understandable to a diverse audience of professionals. Translate the user's industry-specific terms into relatable concepts and simple metaphors.5. OUTPUT FORMAT (MANDATORY)You MUST return your response as a valid JSON object. The presentation_concepts array must contain exactly two concept objects. Adhere strictly to this enhanced schema:{\n  \"presentation_concepts\": [\n    {\n      \"title\": \"A short, catchy, and descriptive title for Concept 1.\",\n      \"idea\": \"A one-sentence summary of the core strategic idea or theme.\",\n      \"structure\": [\n        { \"stage\": \"Intro (1 min)\", \"task\": \"A brief description of the opening and hook.\" },\n        { \"stage\": \"The Problem (2 mins)\", \"task\": \"Description of the problem/pain point being addressed.\" },\n        { \"stage\": \"Our Solution (3 mins)\", \"task\": \"How the user's service solves the problem, woven with a story and highlighting their USP.\" },\n        { \"stage\": \"The Ask (2 mins)\", \"task\": \"The specific call to action, closing statement, and post-presentation follow-up.\" }\n      ],\n      \"hook\": \"The exact suggested opening line or question to grab attention.\",\n      \"ask\": \"The detailed, specific, and actionable referral request for the chapter.\",\n      \"visual_idea\": \"A suggestion for a key slide or a physical prop to make the presentation more memorable.\",\n      \"pro_tip\": \"An expert BNI tip for this concept, e.g., 'To maximize impact, announce this topic 5 weeks in advance and encourage members to invite visitors from the [Target Industry] you've identified.'\"\n    },\n    {\n      \"title\": \"A short, catchy, and descriptive title for Concept 2.\",\n      \"idea\": \"A one-sentence summary of a different strategic idea or theme.\",\n      \"structure\": [\n        { \"stage\": \"Intro (1 min)\", \"task\": \"A brief description of the opening and hook.\" },\n        { \"stage\": \"The Problem (2 mins)\", \"task\": \"Description of the problem/pain point being addressed from a different angle.\" },\n        { \"stage\": \"Our Solution (3 mins)\", \"task\": \"How the user's service solves this problem, using a different story or metaphor and highlighting their USP.\" },\n        { \"stage\": \"The Ask (2 mins)\", \"task\": \"A different, but still highly specific, call to action and closing.\" }\n      ],\n      \"hook\": \"A different compelling opening line or question.\",\n      \"ask\": \"A second detailed, specific, and actionable referral request.\",\n      \"visual_idea\": \"A different suggestion for a prop or visual aid relevant to this concept.\",\n      \"pro_tip\": \"Another expert BNI tip, e.g., 'Since this is a Relationship ask, your closing should strongly encourage scheduling 1-to-1s with your power team members to reinforce the message.'\"\n    }\n  ]\n}\n6. CRITICAL AVOIDANCESDO NOT create a sales pitch. The goal is education and credibility.DO NOT suggest ending the presentation with a Q&A session. The closing must be a strong, controlled Call to Action.DO NOT be vague. Every element must be specific.DO NOT simply list the user's services. Frame them as solutions to problems.You are now The BNI Architect. Analyze the inputs and craft two masterpiece presentation concepts.", "maxIterations": 10}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [680, 300], "id": "ai-agent", "name": "BNI Presentation AI Agent"}, {"parameters": {"contextWindowLength": 10}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [828, 520], "id": "memory-buffer", "name": "Conversation Memory"}, {"parameters": {"model": "deepseek/deepseek-chat-v3-0324", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [708, 520], "id": "openrouter-model", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "yn3czriQEae3D6kG", "name": "OpenRouter account"}}}, {"parameters": {"jsCode": "// Parse AI Agent response and structure for database\nconst response = $input.item.json;\nlet concepts;\n\ntry {\n  // Extract JSON from AI response\n  const content = response.output || response.text || JSON.stringify(response);\n  console.log('AI Agent Response:', content);\n  \n  // Try to parse JSON from response\n  const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n  if (jsonMatch) {\n    concepts = JSON.parse(jsonMatch[0]);\n  } else {\n    throw new Error('No valid JSON found in AI response');\n  }\n  \n  // Validate structure\n  if (!concepts.concept1 || !concepts.concept2) {\n    throw new Error('Missing concept1 or concept2 in AI response');\n  }\n  \n  console.log('Successfully parsed concepts');\n  \n} catch (error) {\n  console.error('Error parsing AI response:', error.message);\n  throw new Error(`Failed to parse AI response: ${error.message}`);\n}\n\n// Get original data\nconst originalData = $('Data Preparation').item.json;\n\n// Structure for database insertion\nreturn {\n  json: {\n    presentation_id: originalData.presentation_id,\n    business_name: originalData.business_info.name,\n    concept1: concepts.concept1,\n    concept2: concepts.concept2,\n    processing_completed_at: new Date().toISOString()\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 400], "id": "response-parser", "name": "Parse AI Response"}], "connections": {"Webhook Trigger": {"main": [[{"node": "Data Preparation", "type": "main", "index": 0}]]}, "Data Preparation": {"main": [[{"node": "BNI Presentation AI Agent", "type": "main", "index": 0}]]}, "BNI Presentation AI Agent": {"main": [[{"node": "Parse AI Response", "type": "main", "index": 0}]]}, "Conversation Memory": {"ai_memory": [[{"node": "BNI Presentation AI Agent", "type": "ai_memory", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "BNI Presentation AI Agent", "type": "ai_languageModel", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "a5d4332b-fe98-4bff-ad34-5fb77e769665", "triggerCount": 1, "shared": [{"createdAt": "2025-06-07T23:05:52.562Z", "updatedAt": "2025-06-07T23:05:52.562Z", "role": "workflow:owner", "workflowId": "syQBLV9AerRPorGv", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}