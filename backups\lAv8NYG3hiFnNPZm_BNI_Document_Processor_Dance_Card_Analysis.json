{"createdAt": "2025-06-08T09:36:41.542Z", "updatedAt": "2025-06-08T12:16:15.000Z", "id": "lAv8NYG3hiFnNPZm", "name": "BNI Document Processor - Dance Card Analysis", "active": false, "isArchived": false, "nodes": [{"parameters": {"path": "bni-document-processor", "options": {"allowedOrigins": "*"}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 321.25], "id": "webhook-trigger", "name": "Document Webhook", "webhookId": "f29c10c2-e3cc-429d-ba4a-6d6ce2805eef"}, {"parameters": {"jsCode": "// Validate incoming data\nconst payload = $input.item.json;\n\n// Log incoming data\nconsole.log('Received document processing request:', JSON.stringify(payload, null, 2));\n\n// Validate required fields\nif (!payload.document_url) {\n  throw new Error('Missing required field: document_url');\n}\n\n// Extract file extension\nconst url = payload.document_url;\nconst fileExtension = url.split('.').pop().toLowerCase();\nconst isPDF = fileExtension === 'pdf';\nconst isImage = ['jpg', 'jpeg', 'png'].includes(fileExtension);\n\nif (!isPDF && !isImage) {\n  throw new Error(`Unsupported file type: ${fileExtension}`);\n}\n\nreturn {\n  json: {\n    document_url: payload.document_url,\n    presentation_id: payload.presentation_id || null,\n    file_type: isPDF ? 'pdf' : 'image',\n    file_extension: fileExtension,\n    metadata: payload\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 321.25], "id": "validate-input", "name": "Validate Input"}, {"parameters": {"url": "={{ $json.document_url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 321.25], "id": "download-file", "name": "Download Document"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "c7e0b7fb-c87e-4740-86a7-5f2e68286a30", "leftValue": "={{ $('Validate Input').item.json.file_type }}", "rightValue": "pdf", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 321.25], "id": "check-file-type", "name": "Is PDF?"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.documentBinaryInputLoader", "typeVersion": 1, "position": [1080, 180], "id": "pdf-loader", "name": "PDF Text Extractor"}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": ""}, "messages": {"values": [{"content": "=You are analyzing a BNI (Business Networking International) dance card image. Extract all business information you can see, including:\n\n1. Member names\n2. Business names\n3. Business categories/professions\n4. Contact information (if visible)\n5. Any notes or special information\n\nReturn the information in a structured JSON format:\n{\n  \"members\": [\n    {\n      \"name\": \"...\",\n      \"business\": \"...\",\n      \"category\": \"...\",\n      \"contact\": \"...\",\n      \"notes\": \"...\"\n    }\n  ],\n  \"total_members\": 0,\n  \"chapter_info\": \"...\",\n  \"other_observations\": \"...\"\n}\n\nBe thorough but only include information you can actually see in the image."}]}, "options": {"maxTokens": 2000, "temperature": 0.3}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.4, "position": [800, 660], "id": "image-analyzer", "name": "Image Analyzer", "credentials": {"openAiApi": {"id": "fX0OnNcYkogf5cCH", "name": "OpenAI account"}}}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [1496, 321.25], "id": "merge-paths", "name": "Merge Document Data"}, {"parameters": {"options": {"systemMessage": "You are an expert at analyzing BNI dance cards and identifying business networking opportunities. You understand the referral system and can spot potential connections between different business categories."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [1716, 321.25], "id": "analyze-content", "name": "BNI Analysis Agent"}, {"parameters": {"options": {"maxTokens": 2000, "temperature": 0.3}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [1804, 541.25], "id": "openai-model", "name": "OpenAI Model", "credentials": {"openAiApi": {"id": "fX0OnNcYkogf5cCH", "name": "OpenAI account"}}}, {"parameters": {"jsCode": "// Parse the AI analysis and structure for database\nconst aiResponse = $input.item.json;\nconst originalData = $('Validate Input').item.json;\n\ntry {\n  // Extract the actual content from the AI response\n  let analysisContent;\n  \n  if (typeof aiResponse.output === 'string') {\n    // Try to parse JSON from the output string\n    const jsonMatch = aiResponse.output.match(/\\{[\\s\\S]*\\}/);\n    if (jsonMatch) {\n      analysisContent = JSON.parse(jsonMatch[0]);\n    } else {\n      throw new Error('No valid JSON found in AI response');\n    }\n  } else if (aiResponse.text) {\n    analysisContent = JSON.parse(aiResponse.text);\n  } else {\n    analysisContent = aiResponse;\n  }\n\n  // Structure the data for storage\n  return {\n    json: {\n      presentation_id: originalData.presentation_id,\n      document_url: originalData.document_url,\n      file_type: originalData.file_type,\n      extraction_data: analysisContent,\n      extracted_at: new Date().toISOString(),\n      status: 'completed'\n    }\n  };\n\n} catch (error) {\n  console.error('Error parsing AI response:', error);\n  return {\n    json: {\n      presentation_id: originalData.presentation_id,\n      document_url: originalData.document_url,\n      file_type: originalData.file_type,\n      extraction_data: { error: error.message, raw_response: aiResponse },\n      extracted_at: new Date().toISOString(),\n      status: 'error'\n    }\n  };\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2092, 321.25], "id": "structure-data", "name": "Structure Results"}, {"parameters": {"tableId": "dance_card_extractions", "fieldsUi": {"fieldValues": [{"fieldId": "presentation_id", "fieldValue": "={{ $json.presentation_id }}"}, {"fieldId": "document_url", "fieldValue": "={{ $json.document_url }}"}, {"fieldId": "file_type", "fieldValue": "={{ $json.file_type }}"}, {"fieldId": "extraction_data", "fieldValue": "={{ JSON.stringify($json.extraction_data) }}"}, {"fieldId": "status", "fieldValue": "={{ $json.status }}"}, {"fieldId": "extracted_at", "fieldValue": "={{ $json.extracted_at }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2312, 321.25], "id": "save-to-database", "name": "Save Extraction", "credentials": {"supabaseApi": {"id": "MaYgyPFsCO1qMHKL", "name": "Supabase account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  success: true,\n  message: 'Document processed successfully',\n  presentation_id: $json.presentation_id,\n  extraction_summary: {\n    file_type: $json.file_type,\n    status: $json.status,\n    extracted_at: $json.extracted_at,\n    data_preview: $json.extraction_data\n  }\n} }}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2532, 321.25], "id": "success-response", "name": "Success Response"}, {"parameters": {}, "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [240, 681.25], "id": "error-trigger", "name": "<PERSON><PERSON><PERSON>"}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  success: false,\n  message: 'Error processing document',\n  error: $json.error?.message || 'Unknown error occurred',\n  details: $json\n} }}", "options": {"responseCode": 500}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 681.25], "id": "error-response", "name": "Error Response"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1.1, "position": [1120, 500], "id": "dabcb3b8-995e-4b37-a7d9-b3738fb3aba1", "name": "Information Extractor"}, {"parameters": {"model": "openai/gpt-4.1-mini", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [1200, 740], "id": "52c92367-3f10-46bb-8dd0-af7677427299", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "yn3czriQEae3D6kG", "name": "OpenRouter account"}}}], "connections": {"Document Webhook": {"main": [[{"node": "Validate Input", "type": "main", "index": 0}]]}, "Validate Input": {"main": [[{"node": "Download Document", "type": "main", "index": 0}]]}, "Download Document": {"main": [[{"node": "Is PDF?", "type": "main", "index": 0}]]}, "Is PDF?": {"main": [[{"node": "PDF Text Extractor", "type": "main", "index": 0}], [{"node": "Information Extractor", "type": "main", "index": 0}]]}, "PDF Text Extractor": {"main": [[{"node": "Merge Document Data", "type": "main", "index": 0}]]}, "Image Analyzer": {"main": [[]]}, "Merge Document Data": {"main": [[{"node": "BNI Analysis Agent", "type": "main", "index": 0}]]}, "BNI Analysis Agent": {"main": [[{"node": "Structure Results", "type": "main", "index": 0}]]}, "OpenAI Model": {"ai_languageModel": [[{"node": "BNI Analysis Agent", "type": "ai_languageModel", "index": 0}]]}, "Structure Results": {"main": [[{"node": "Save Extraction", "type": "main", "index": 0}]]}, "Save Extraction": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Error Handler": {"main": [[{"node": "Error Response", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "Information Extractor", "type": "ai_languageModel", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "Merge Document Data", "type": "main", "index": 1}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "9799515a-829a-4a68-8793-9259f271624b", "triggerCount": 1, "shared": [{"createdAt": "2025-06-08T09:36:41.545Z", "updatedAt": "2025-06-08T09:36:41.545Z", "role": "workflow:owner", "workflowId": "lAv8NYG3hiFnNPZm", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}