{"createdAt": "2025-06-08T08:49:19.559Z", "updatedAt": "2025-06-08T08:49:19.559Z", "id": "xh2mbNMbrd69UQfc", "name": "Supabase_Postgres", "active": false, "isArchived": false, "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-440, 260], "id": "dff5092a-47ad-4e83-9538-c0c629e634ea", "name": "When chat message received", "webhookId": "679e356b-fcc3-4abc-ab59-8ca4ce2cc616"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-380, 480], "id": "7de5668c-3a49-4ae6-ae94-c66b2f596550", "name": "OpenAI Chat Model"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [-220, 480], "id": "1379e48b-e285-4a12-a184-0a1bd4fd0a2a", "name": "Postgres Chat Memory"}, {"parameters": {"mode": "retrieve-as-tool", "tableName": {"__rl": true, "mode": "list", "value": ""}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [-80, 480], "id": "28d02882-443c-4d36-970d-ef19a5a327a4", "name": "Supabase Vector Store"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [-200, 1120], "id": "9cb14e91-f3e9-4826-a035-2fa0f7a89a42", "name": "Embeddings OpenAI"}, {"parameters": {"dataType": "binary", "options": {}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [0, 1120], "id": "781e57e7-fcee-46da-a7d9-ef9caa514150", "name": "Default Data Loader"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [100, 1340], "id": "f7c56584-425f-421f-93af-c2583f531d5f", "name": "Recursive Character Text Splitter"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-600, 900], "id": "82e97817-d99c-4fb4-9918-987c5c2428d9", "name": "When clicking 'Test workflow'"}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "mode": "list", "value": ""}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [-140, 900], "id": "a8e150be-2d5a-4480-9e22-f71da7dab5de", "name": "Add to Supabase"}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "mode": "list", "value": ""}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-360, 900], "id": "69c80d00-09ac-4500-a567-adb929c6626d", "name": "Download File"}, {"parameters": {"options": {"systemMessage": "You are a helpful assistant"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-220, 260], "id": "d0cce173-254b-4098-be85-52bbf3f71e32", "name": "RAG Agent"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [-20, 640], "id": "a5809245-404c-44df-863c-8d7171941e60", "name": "Embeddings"}, {"parameters": {"content": "# Loading Binary Data from Google Drive", "height": 760, "width": 1140}, "type": "n8n-nodes-base.stickyNote", "position": [-740, 780], "typeVersion": 1, "id": "a70c809c-1e8a-4526-9443-8d309b2899f4", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "# RAG Agent with Memory\n", "height": 660, "width": 1140, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-740, 120], "typeVersion": 1, "id": "493592ff-36a3-4407-936b-89be1aaa4027", "name": "Sticky Note1"}, {"parameters": {"content": "# <PERSON> | AI Automation", "height": 100, "width": 500, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [-400, -20], "typeVersion": 1, "id": "889d43f1-96d4-49be-8ab5-f3b6b099d6de", "name": "Sticky Note2"}], "connections": {"When chat message received": {"main": [[{"node": "RAG Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "RAG Agent", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "RAG Agent", "type": "ai_memory", "index": 0}]]}, "Supabase Vector Store": {"ai_tool": [[{"node": "RAG Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Add to Supabase", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Add to Supabase", "type": "ai_document", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When clicking 'Test workflow'": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Download File": {"main": [[{"node": "Add to Supabase", "type": "main", "index": 0}]]}, "Embeddings": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": null, "pinData": {}, "versionId": "29e02b8c-ad46-4a27-9476-b24752f7f4ab", "triggerCount": 0, "shared": [{"createdAt": "2025-06-08T08:49:19.566Z", "updatedAt": "2025-06-08T08:49:19.566Z", "role": "workflow:owner", "workflowId": "xh2mbNMbrd69UQfc", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}