{"createdAt": "2025-06-08T11:21:59.673Z", "updatedAt": "2025-06-08T11:47:02.000Z", "id": "nWzd60SsNCOJM8oi", "name": "BNI Test Webhook - Data Reception Verification", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "bni-test-connection", "options": {"allowedOrigins": "https://bni-presentation-generator.netlify.app,http://localhost:5173,http://localhost:5174,http://localhost:3000,http://localhost:4173"}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "id": "webhook-trigger", "name": "Webhook Trigger", "webhookId": "test-bni-connection"}, {"parameters": {"jsCode": "// Log all incoming data for debugging\nconst webhookData = $input.item.json;\nconst headers = $input.item.json.headers || {};\nconst query = $input.item.json.query || {};\nconst body = $input.item.json.body || {};\n\nconsole.log('=== WEBHOOK DATA RECEIVED ===');\nconsole.log('Timestamp:', new Date().toISOString());\nconsole.log('Full webhook data:', JSON.stringify(webhookData, null, 2));\nconsole.log('Headers:', JSON.stringify(headers, null, 2));\nconsole.log('Query params:', JSON.stringify(query, null, 2));\nconsole.log('Body:', JSON.stringify(body, null, 2));\nconsole.log('============================');\n\n// Extract expected fields from the app\nconst expectedFields = [\n  'presentation_id',\n  'business_name',\n  'industry',\n  'products_services',\n  'target_audience',\n  'presentation_goals',\n  'initial_ideas',\n  'dance_card_url'\n];\n\nconst receivedFields = {};\nconst missingFields = [];\n\nexpectedFields.forEach(field => {\n  if (body[field]) {\n    receivedFields[field] = body[field];\n  } else {\n    missingFields.push(field);\n  }\n});\n\nreturn {\n  json: {\n    timestamp: new Date().toISOString(),\n    webhook_received: true,\n    data_structure: {\n      headers_count: Object.keys(headers).length,\n      query_params_count: Object.keys(query).length,\n      body_fields_count: Object.keys(body).length,\n      received_fields: receivedFields,\n      missing_fields: missingFields\n    },\n    raw_data: {\n      headers: headers,\n      query: query,\n      body: body\n    }\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300], "id": "data-logger", "name": "Log & Analyze Data"}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"Webhook test successful - Data received\",\n  \"timestamp\": $json.timestamp,\n  \"data_analysis\": $json.data_structure,\n  \"webhook_path\": \"bni-test-connection\",\n  \"execution_id\": $execution.id\n} }}", "options": {"responseCode": 200, "responseHeaders": {"entries": [{"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Methods", "value": "POST, OPTIONS"}, {"name": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"name": "Content-Type", "value": "application/json"}]}}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300], "id": "respond-success", "name": "Respond Success"}], "connections": {"Webhook Trigger": {"main": [[{"node": "Log & Analyze Data", "type": "main", "index": 0}]]}, "Log & Analyze Data": {"main": [[{"node": "Respond Success", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": null, "pinData": null, "versionId": "e426d19e-4088-473b-bafe-15bef1b5e4d8", "triggerCount": 1, "shared": [{"createdAt": "2025-06-08T11:21:59.677Z", "updatedAt": "2025-06-08T11:21:59.677Z", "role": "workflow:owner", "workflowId": "nWzd60SsNCOJM8oi", "projectId": "jncwk4CjUI6FYXvX", "project": {"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:14:27.401Z", "id": "jncwk4CjUI6FYXvX", "name": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "personal", "icon": null, "projectRelations": [{"createdAt": "2025-05-30T21:10:52.874Z", "updatedAt": "2025-05-30T21:10:52.874Z", "role": "project:personal<PERSON><PERSON>er", "userId": "313fd0ca-8649-4143-b983-8f510628ee33", "projectId": "jncwk4CjUI6FYXvX", "user": {"createdAt": "2025-05-30T21:10:52.508Z", "updatedAt": "2025-06-30T08:13:13.474Z", "id": "313fd0ca-8649-4143-b983-8f510628ee33", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Jagtap", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-05-30T21:15:02.563Z", "personalization_survey_n8n_version": "1.94.1", "companySize": "<20", "companyType": "saas", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "31t9nWTe22naF3Nh", "userActivatedAt": 1748688873809, "npsSurvey": {"responded": true, "lastShownAt": 1751271190651}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}