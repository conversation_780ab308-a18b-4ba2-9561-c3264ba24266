{"createdAt": "2025-06-07T19:05:48.422Z", "updatedAt": "2025-06-07T21:50:02.000Z", "id": "9vr6XGinOFYqKAKy", "name": "Presentation 1.0", "active": false, "isArchived": false, "workflow_summary": {"description": "BNI Feature Presentation generator workflow - appears to be incomplete/disconnected", "key_features": ["Webhook endpoint for presentation generation", "Data validation for business information", "Dance card processing capability", "OpenAI GPT-4 integration for concept generation", "Dual concept generation (2 different approaches)", "Supabase database integration", "Error handling and status updates"], "main_components": ["Webhook trigger", "Data validation and preparation", "Optional dance card download and processing", "AI prompt preparation", "OpenAI API calls for concept generation", "JSON parsing and formatting", "Database operations", "Response handling"], "business_logic": ["Validates required business information", "Optionally processes dance card files", "Generates two different presentation concepts", "Saves results to Supabase database", "Returns structured JSON response"], "integrations": ["OpenAI GPT-4 API", "Supabase database", "Webhook endpoints"], "note": "This workflow has 18 nodes but NO CONNECTIONS defined, indicating it's incomplete or disconnected. The nodes suggest a comprehensive BNI presentation generation system but would need proper connections to function."}, "connections": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": null, "pinData": {}, "versionId": "12d23836-6551-49a7-afcd-c1a0d31afde7", "triggerCount": 1, "shared": [{"createdAt": "2025-06-07T19:05:48.427Z", "updatedAt": "2025-06-07T19:05:48.427Z", "role": "workflow:owner", "workflowId": "9vr6XGinOFYqKAKy", "projectId": "jncwk4CjUI6FYXvX"}], "tags": []}