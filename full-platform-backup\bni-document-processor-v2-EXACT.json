{"createdAt": "2025-06-08T12:22:41.843Z", "updatedAt": "2025-06-08T22:14:08.000Z", "id": "J4GmoGWcrMz2argl", "name": "BNI Document Processor v2 - OpenRouter Agent", "active": false, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "bni-document-processor-v2", "responseMode": "responseNode", "options": {"allowedOrigins": "https://bni-presentation-generator.netlify.app,http://localhost:5173,http://localhost:5174,http://localhost:3000,http://localhost:4173"}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 240], "id": "webhook-node", "name": "Document Webhook", "webhookId": "doc-processor-v2"}, {"parameters": {"jsCode": "  // Validate and log incoming data\n  const payload = $input.item.json.body || $input.item.json;\n\n  console.log(\"=== DOCUMENT PROCESSING REQUEST ===\");\n  console.log(\"Timestamp:\", new Date().toISOString());\n  console.log(\"Payload:\", JSON.stringify(payload, null, 2));\n\n  // Check if this is a presentation request with dance_card_url\n  let document_url;\n  let presentation_id;\n\n  if (payload.dance_card_url) {\n    // This is a full presentation request\n    document_url = payload.dance_card_url;\n    presentation_id = payload.presentation_id;\n    console.log(\"Processing presentation dance card:\", document_url);\n  } else if (payload.document_url) {\n    // This is a direct document request\n    document_url = payload.document_url;\n    presentation_id = payload.presentation_id || null;\n  } else {\n    throw new Error(\"Missing required field: document_url or dance_card_url\");\n  }\n\n  // Extract file info\n  const url = document_url;\n  const fileExtension = url.split(\".\").pop().toLowerCase().split(\"?\")[0]; // Handle URLs with query params\n  const isPDF = fileExtension === \"pdf\";\n  const isImage = [\"jpg\", \"jpeg\", \"png\"].includes(fileExtension);\n\n  if (!isPDF && !isImage) {\n    throw new Error(`Unsupported file type: ${fileExtension}`);\n  }\n\n  return {\n    json: {\n      document_url: document_url,\n      presentation_id: presentation_id,\n      file_type: isPDF ? \"pdf\" : \"image\",\n      file_extension: fileExtension,\n      timestamp: new Date().toISOString(),\n      original_payload: payload,\n      // Store presentation data if available\n      presentation_data: payload.dance_card_url ? {\n        business_name: payload.business_name,\n        industry: payload.industry,\n        products_services: payload.products_services,\n        target_audience: payload.target_audience,\n        presentation_goals: payload.presentation_goals,\n        initial_ideas: payload.initial_ideas\n      } : null\n    }\n  };"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 240], "id": "validate-node", "name": "Validate & Prepare Data"}], "connections": {"Document Webhook": {"main": [[{"node": "Validate & Prepare Data", "type": "main", "index": 0}]]}}, "settings": {"saveExecutionProgress": true, "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "executionTimeout": 3600, "timezone": "UTC"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "versionId": "a226203b-4ff1-43e9-af0c-95549f414ae2", "triggerCount": 1, "backup_metadata": {"backup_status": "EXACT_REPLICA_FROM_N8N", "backup_timestamp": "2025-01-03T00:00:00Z", "source": "n8n_mcp_get_workflow", "workflow_complexity": "high", "node_count": 10}}